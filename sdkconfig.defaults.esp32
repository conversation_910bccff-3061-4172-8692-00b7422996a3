CONFIG_ESP32_DEFAULT_CPU_FREQ_240=y
CONFIG_ESP32_DEFAULT_CPU_FREQ_MHZ=240
CONFIG_SDIO_DAT2_DISABLED=

# BT Configuration
CONFIG_BT_ENABLED=y
CONFIG_BT_CONTROLLER_ONLY=y
CONFIG_BT_BLUEDROID_ENABLED=
CONFIG_BTDM_CONTROLLER_MODE_BTDM=y
CONFIG_BTDM_CONTROLLER_HCI_MODE_VHCI=y
CONFIG_BTDM_CTRL_AUTO_LATENCY=y
CONFIG_BTDM_CONTROLLER_MODEM_SLEEP=y
CONFIG_BTDM_CTRL_MODEM_SLEEP=y

# BT over UART
#CONFIG_BTDM_CONTROLLER_HCI_MODE_UART_H4=y
#CONFIG_BTDM_CTRL_HCI_MODE_UART_H4=y
#CONFIG_BT_HCI_UART_NO=1
#CONFIG_BT_HCI_UART_BAUDRATE=921600

#CO-EX config
CONFIG_FREERTOS_UNICORE=n
CONFIG_FREERTOS_HZ=400
CONFIG_ESP32_WIFI_TASK_PINNED_TO_CORE_1=y
CONFIG_BTDM_CONTROLLER_PINNED_TO_CORE=0
CONFIG_BTDM_CTRL_PINNED_TO_CORE=0
CONFIG_BTDM_CTRL_PINNED_TO_CORE_0=y

CONFIG_ESP32_WIFI_STATIC_RX_BUFFER_NUM=16
CONFIG_ESP32_WIFI_DYNAMIC_RX_BUFFER_NUM=32
CONFIG_ESP32_WIFI_DYNAMIC_TX_BUFFER=y
CONFIG_ESP32_WIFI_DYNAMIC_TX_BUFFER_NUM=64

# OTA
CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_PARTITION_TABLE_CUSTOM_FILENAME="partitions.esp32.csv"

# iram text saving
CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH=y

CONFIG_ESP32_DEFAULT_CPU_FREQ_160=y
CONFIG_ESP32_DEFAULT_CPU_FREQ_MHZ=160
CONFIG_FREERTOS_UNICORE=y
CONFIG_SDIO_DAT2_DISABLED=

# BT Configuration
CONFIG_BT_ENABLED=n ## temporary disable
CONFIG_BT_CONTROLLER_ONLY=y
CONFIG_BT_BLUEDROID_ENABLED=
CONFIG_BT_LE_SLEEP_ENABLE=y

#UART pins, Enable below config, delete sdkconfig and rebuild
#CONFIG_BT_LE_HCI_INTERFACE_USE_UART=y
#CONFIG_BT_LE_HCI_UART_FLOWCTRL=n
#CONFIG_BT_LE_HCI_UART_TX_PIN=5
#CONFIG_BT_LE_HCI_UART_RX_PIN=18

CONFIG_ESPTOOLPY_FLASHSIZE_4MB=n
CONFIG_ESPTOOLPY_FLASHSIZE_2MB=y
CONFIG_PARTITION_TABLE_TWO_OTA=y
CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_PARTITION_TABLE_CUSTOM_FILENAME="partitions.esp32c2.csv"
CONFIG_PARTITION_TABLE_FILENAME="partitions.esp32c2.csv"

CONFIG_ESP_DEFAULT_TASK_STACK_SIZE=2048

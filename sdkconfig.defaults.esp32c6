CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_160=y

# BT Configuration
CONFIG_BT_ENABLED=y
CONFIG_BT_CONTROLLER_ONLY=y
CONFIG_BT_BLUEDROID_ENABLED=
CONFIG_BT_LE_SLEEP_ENABLE=y

# SPI/SDIO only
CONFIG_BT_LE_HCI_INTERFACE_USE_RAM=y

# BLE over UART:
# Enable below config, delete sdkconfig, build/ and rebuild
# CONFIG_BT_LE_HCI_INTERFACE_USE_UART=y
# CONFIG_BT_LE_HCI_UART_TX_PIN=5
# CONFIG_BT_LE_HCI_UART_RX_PIN=12
# CONFIG_BT_LE_HCI_UART_FLOWCTRL=n
# # 4 pin solution is not working yet for c6
# #CONFIG_BT_LE_HCI_UART_RTS_PIN=9
# #CONFIG_BT_LE_HCI_UART_CTS_PIN=13

CONFIG_ESP_CACHE_MALLOC=n

CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_PARTITION_TABLE_CUSTOM_FILENAME="partitions.esp32c6.csv"
CONFIG_ESP_PKT_STATS=y

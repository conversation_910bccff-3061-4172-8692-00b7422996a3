/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: esp_hosted_rpc.proto */

#ifndef PROTOBUF_C_esp_5fhosted_5frpc_2eproto__INCLUDED
#define PROTOBUF_C_esp_5fhosted_5frpc_2eproto__INCLUDED

#include <protobuf-c/protobuf-c.h>

PROTOBUF_C__BEGIN_DECLS

#if PROTOBUF_C_VERSION_NUMBER < 1003000
# error This file was generated by a newer version of protoc-c which is incompatible with your libprotobuf-c headers. Please update your headers.
#elif 1004001 < PROTOBUF_C_MIN_COMPILER_VERSION
# error This file was generated by an older version of protoc-c which is incompatible with your libprotobuf-c headers. Please regenerate this file with a newer version of protoc-c.
#endif


typedef struct WifiInitConfig WifiInitConfig;
typedef struct WifiCountry WifiCountry;
typedef struct WifiActiveScanTime WifiActiveScanTime;
typedef struct WifiScanTime WifiScanTime;
typedef struct WifiScanConfig WifiScanConfig;
typedef struct WifiHeApInfo WifiHeApInfo;
typedef struct WifiApRecord WifiApRecord;
typedef struct WifiScanThreshold WifiScanThreshold;
typedef struct WifiPmfConfig WifiPmfConfig;
typedef struct WifiApConfig WifiApConfig;
typedef struct WifiStaConfig WifiStaConfig;
typedef struct WifiConfig WifiConfig;
typedef struct WifiStaInfo WifiStaInfo;
typedef struct WifiStaList WifiStaList;
typedef struct WifiPktRxCtrl WifiPktRxCtrl;
typedef struct WifiPromiscuousPkt WifiPromiscuousPkt;
typedef struct WifiPromiscuousFilter WifiPromiscuousFilter;
typedef struct WifiCsiConfig WifiCsiConfig;
typedef struct WifiCsiInfo WifiCsiInfo;
typedef struct WifiAntGpio WifiAntGpio;
typedef struct WifiAntGpioConfig WifiAntGpioConfig;
typedef struct WifiAntConfig WifiAntConfig;
typedef struct WifiActionTxReq WifiActionTxReq;
typedef struct WifiFtmInitiatorCfg WifiFtmInitiatorCfg;
typedef struct WifiEventStaScanDone WifiEventStaScanDone;
typedef struct WifiEventStaConnected WifiEventStaConnected;
typedef struct WifiEventStaDisconnected WifiEventStaDisconnected;
typedef struct WifiEventStaAuthmodeChange WifiEventStaAuthmodeChange;
typedef struct WifiEventStaWpsErPin WifiEventStaWpsErPin;
typedef struct ApCred ApCred;
typedef struct WifiEventStaWpsErSuccess WifiEventStaWpsErSuccess;
typedef struct WifiEventApProbeReqRx WifiEventApProbeReqRx;
typedef struct WifiEventBssRssiLow WifiEventBssRssiLow;
typedef struct WifiFtmReportEntry WifiFtmReportEntry;
typedef struct WifiEventFtmReport WifiEventFtmReport;
typedef struct WifiEventActionTxStatus WifiEventActionTxStatus;
typedef struct WifiEventRocDone WifiEventRocDone;
typedef struct WifiEventApWpsRgPin WifiEventApWpsRgPin;
typedef struct WifiEventApWpsRgFailReason WifiEventApWpsRgFailReason;
typedef struct WifiEventApWpsRgSuccess WifiEventApWpsRgSuccess;
typedef struct WifiProtocols WifiProtocols;
typedef struct WifiBandwidths WifiBandwidths;
typedef struct ConnectedSTAList ConnectedSTAList;
typedef struct RpcReqGetMacAddress RpcReqGetMacAddress;
typedef struct RpcRespGetMacAddress RpcRespGetMacAddress;
typedef struct RpcReqGetMode RpcReqGetMode;
typedef struct RpcRespGetMode RpcRespGetMode;
typedef struct RpcReqSetMode RpcReqSetMode;
typedef struct RpcRespSetMode RpcRespSetMode;
typedef struct RpcReqGetPs RpcReqGetPs;
typedef struct RpcRespGetPs RpcRespGetPs;
typedef struct RpcReqSetPs RpcReqSetPs;
typedef struct RpcRespSetPs RpcRespSetPs;
typedef struct RpcReqSetMacAddress RpcReqSetMacAddress;
typedef struct RpcRespSetMacAddress RpcRespSetMacAddress;
typedef struct RpcReqOTABegin RpcReqOTABegin;
typedef struct RpcRespOTABegin RpcRespOTABegin;
typedef struct RpcReqOTAWrite RpcReqOTAWrite;
typedef struct RpcRespOTAWrite RpcRespOTAWrite;
typedef struct RpcReqOTAEnd RpcReqOTAEnd;
typedef struct RpcRespOTAEnd RpcRespOTAEnd;
typedef struct RpcReqWifiSetMaxTxPower RpcReqWifiSetMaxTxPower;
typedef struct RpcRespWifiSetMaxTxPower RpcRespWifiSetMaxTxPower;
typedef struct RpcReqWifiGetMaxTxPower RpcReqWifiGetMaxTxPower;
typedef struct RpcRespWifiGetMaxTxPower RpcRespWifiGetMaxTxPower;
typedef struct RpcReqConfigHeartbeat RpcReqConfigHeartbeat;
typedef struct RpcRespConfigHeartbeat RpcRespConfigHeartbeat;
typedef struct RpcReqWifiInit RpcReqWifiInit;
typedef struct RpcRespWifiInit RpcRespWifiInit;
typedef struct RpcReqWifiDeinit RpcReqWifiDeinit;
typedef struct RpcRespWifiDeinit RpcRespWifiDeinit;
typedef struct RpcReqWifiSetConfig RpcReqWifiSetConfig;
typedef struct RpcRespWifiSetConfig RpcRespWifiSetConfig;
typedef struct RpcReqWifiGetConfig RpcReqWifiGetConfig;
typedef struct RpcRespWifiGetConfig RpcRespWifiGetConfig;
typedef struct RpcReqWifiConnect RpcReqWifiConnect;
typedef struct RpcRespWifiConnect RpcRespWifiConnect;
typedef struct RpcReqWifiDisconnect RpcReqWifiDisconnect;
typedef struct RpcRespWifiDisconnect RpcRespWifiDisconnect;
typedef struct RpcReqWifiStart RpcReqWifiStart;
typedef struct RpcRespWifiStart RpcRespWifiStart;
typedef struct RpcReqWifiStop RpcReqWifiStop;
typedef struct RpcRespWifiStop RpcRespWifiStop;
typedef struct RpcReqWifiScanStart RpcReqWifiScanStart;
typedef struct RpcRespWifiScanStart RpcRespWifiScanStart;
typedef struct RpcReqWifiScanStop RpcReqWifiScanStop;
typedef struct RpcRespWifiScanStop RpcRespWifiScanStop;
typedef struct RpcReqWifiScanGetApNum RpcReqWifiScanGetApNum;
typedef struct RpcRespWifiScanGetApNum RpcRespWifiScanGetApNum;
typedef struct RpcReqWifiScanGetApRecords RpcReqWifiScanGetApRecords;
typedef struct RpcRespWifiScanGetApRecords RpcRespWifiScanGetApRecords;
typedef struct RpcReqWifiClearApList RpcReqWifiClearApList;
typedef struct RpcRespWifiClearApList RpcRespWifiClearApList;
typedef struct RpcReqWifiRestore RpcReqWifiRestore;
typedef struct RpcRespWifiRestore RpcRespWifiRestore;
typedef struct RpcReqWifiClearFastConnect RpcReqWifiClearFastConnect;
typedef struct RpcRespWifiClearFastConnect RpcRespWifiClearFastConnect;
typedef struct RpcReqWifiDeauthSta RpcReqWifiDeauthSta;
typedef struct RpcRespWifiDeauthSta RpcRespWifiDeauthSta;
typedef struct RpcReqWifiStaGetApInfo RpcReqWifiStaGetApInfo;
typedef struct RpcRespWifiStaGetApInfo RpcRespWifiStaGetApInfo;
typedef struct RpcReqWifiSetProtocol RpcReqWifiSetProtocol;
typedef struct RpcRespWifiSetProtocol RpcRespWifiSetProtocol;
typedef struct RpcReqWifiGetProtocol RpcReqWifiGetProtocol;
typedef struct RpcRespWifiGetProtocol RpcRespWifiGetProtocol;
typedef struct RpcReqWifiSetBandwidth RpcReqWifiSetBandwidth;
typedef struct RpcRespWifiSetBandwidth RpcRespWifiSetBandwidth;
typedef struct RpcReqWifiGetBandwidth RpcReqWifiGetBandwidth;
typedef struct RpcRespWifiGetBandwidth RpcRespWifiGetBandwidth;
typedef struct RpcReqWifiSetChannel RpcReqWifiSetChannel;
typedef struct RpcRespWifiSetChannel RpcRespWifiSetChannel;
typedef struct RpcReqWifiGetChannel RpcReqWifiGetChannel;
typedef struct RpcRespWifiGetChannel RpcRespWifiGetChannel;
typedef struct RpcReqWifiSetStorage RpcReqWifiSetStorage;
typedef struct RpcRespWifiSetStorage RpcRespWifiSetStorage;
typedef struct RpcReqWifiSetCountryCode RpcReqWifiSetCountryCode;
typedef struct RpcRespWifiSetCountryCode RpcRespWifiSetCountryCode;
typedef struct RpcReqWifiGetCountryCode RpcReqWifiGetCountryCode;
typedef struct RpcRespWifiGetCountryCode RpcRespWifiGetCountryCode;
typedef struct RpcReqWifiSetCountry RpcReqWifiSetCountry;
typedef struct RpcRespWifiSetCountry RpcRespWifiSetCountry;
typedef struct RpcReqWifiGetCountry RpcReqWifiGetCountry;
typedef struct RpcRespWifiGetCountry RpcRespWifiGetCountry;
typedef struct RpcReqWifiApGetStaList RpcReqWifiApGetStaList;
typedef struct RpcRespWifiApGetStaList RpcRespWifiApGetStaList;
typedef struct RpcReqWifiApGetStaAid RpcReqWifiApGetStaAid;
typedef struct RpcRespWifiApGetStaAid RpcRespWifiApGetStaAid;
typedef struct RpcReqWifiStaGetRssi RpcReqWifiStaGetRssi;
typedef struct RpcRespWifiStaGetRssi RpcRespWifiStaGetRssi;
typedef struct RpcReqWifiStaGetAid RpcReqWifiStaGetAid;
typedef struct RpcRespWifiStaGetAid RpcRespWifiStaGetAid;
typedef struct RpcReqWifiSetProtocols RpcReqWifiSetProtocols;
typedef struct RpcRespWifiSetProtocols RpcRespWifiSetProtocols;
typedef struct RpcReqWifiGetProtocols RpcReqWifiGetProtocols;
typedef struct RpcRespWifiGetProtocols RpcRespWifiGetProtocols;
typedef struct RpcReqWifiSetBandwidths RpcReqWifiSetBandwidths;
typedef struct RpcRespWifiSetBandwidths RpcRespWifiSetBandwidths;
typedef struct RpcReqWifiGetBandwidths RpcReqWifiGetBandwidths;
typedef struct RpcRespWifiGetBandwidths RpcRespWifiGetBandwidths;
typedef struct RpcReqWifiSetBand RpcReqWifiSetBand;
typedef struct RpcRespWifiSetBand RpcRespWifiSetBand;
typedef struct RpcReqWifiGetBand RpcReqWifiGetBand;
typedef struct RpcRespWifiGetBand RpcRespWifiGetBand;
typedef struct RpcReqWifiSetBandMode RpcReqWifiSetBandMode;
typedef struct RpcRespWifiSetBandMode RpcRespWifiSetBandMode;
typedef struct RpcReqWifiGetBandMode RpcReqWifiGetBandMode;
typedef struct RpcRespWifiGetBandMode RpcRespWifiGetBandMode;
typedef struct RpcEventWifiEventNoArgs RpcEventWifiEventNoArgs;
typedef struct RpcEventESPInit RpcEventESPInit;
typedef struct RpcEventHeartbeat RpcEventHeartbeat;
typedef struct RpcEventAPStaDisconnected RpcEventAPStaDisconnected;
typedef struct RpcEventAPStaConnected RpcEventAPStaConnected;
typedef struct RpcEventStaScanDone RpcEventStaScanDone;
typedef struct RpcEventStaConnected RpcEventStaConnected;
typedef struct RpcEventStaDisconnected RpcEventStaDisconnected;
typedef struct Rpc Rpc;


/* --- enums --- */

typedef enum _RpcWifiBw {
  RPC__WIFI_BW__BW_Invalid = 0,
  RPC__WIFI_BW__HT20 = 1,
  RPC__WIFI_BW__HT40 = 2
    PROTOBUF_C__FORCE_ENUM_TO_BE_INT_SIZE(RPC__WIFI_BW)
} RpcWifiBw;
typedef enum _RpcWifiPowerSave {
  RPC__WIFI_POWER_SAVE__PS_Invalid = 0,
  RPC__WIFI_POWER_SAVE__MIN_MODEM = 1,
  RPC__WIFI_POWER_SAVE__MAX_MODEM = 2
    PROTOBUF_C__FORCE_ENUM_TO_BE_INT_SIZE(RPC__WIFI_POWER_SAVE)
} RpcWifiPowerSave;
typedef enum _RpcWifiSecProt {
  RPC__WIFI_SEC_PROT__Open = 0,
  RPC__WIFI_SEC_PROT__WEP = 1,
  RPC__WIFI_SEC_PROT__WPA_PSK = 2,
  RPC__WIFI_SEC_PROT__WPA2_PSK = 3,
  RPC__WIFI_SEC_PROT__WPA_WPA2_PSK = 4,
  RPC__WIFI_SEC_PROT__WPA2_ENTERPRISE = 5,
  RPC__WIFI_SEC_PROT__WPA3_PSK = 6,
  RPC__WIFI_SEC_PROT__WPA2_WPA3_PSK = 7
    PROTOBUF_C__FORCE_ENUM_TO_BE_INT_SIZE(RPC__WIFI_SEC_PROT)
} RpcWifiSecProt;
/*
 * enums for Control path 
 */
typedef enum _RpcStatus {
  RPC__STATUS__Connected = 0,
  RPC__STATUS__Not_Connected = 1,
  RPC__STATUS__No_AP_Found = 2,
  RPC__STATUS__Connection_Fail = 3,
  RPC__STATUS__Invalid_Argument = 4,
  RPC__STATUS__Out_Of_Range = 5
    PROTOBUF_C__FORCE_ENUM_TO_BE_INT_SIZE(RPC__STATUS)
} RpcStatus;
typedef enum _RpcType {
  RPC_TYPE__MsgType_Invalid = 0,
  RPC_TYPE__Req = 1,
  RPC_TYPE__Resp = 2,
  RPC_TYPE__Event = 3,
  RPC_TYPE__MsgType_Max = 4
    PROTOBUF_C__FORCE_ENUM_TO_BE_INT_SIZE(RPC_TYPE)
} RpcType;
typedef enum _RpcId {
  RPC_ID__MsgId_Invalid = 0,
  /*
   ** Request Msgs *
   */
  /*
   *0x100
   */
  RPC_ID__Req_Base = 256,
  /*
   *0x101
   */
  RPC_ID__Req_GetMACAddress = 257,
  /*
   *0x102
   */
  RPC_ID__Req_SetMacAddress = 258,
  /*
   *0x103
   */
  RPC_ID__Req_GetWifiMode = 259,
  /*
   *0x104
   */
  RPC_ID__Req_SetWifiMode = 260,
  /*
   *0x10e
   */
  RPC_ID__Req_WifiSetPs = 270,
  /*
   *0x10f
   */
  RPC_ID__Req_WifiGetPs = 271,
  /*
   *0x110
   */
  RPC_ID__Req_OTABegin = 272,
  /*
   *0x111
   */
  RPC_ID__Req_OTAWrite = 273,
  /*
   *0x112
   */
  RPC_ID__Req_OTAEnd = 274,
  /*
   *0x113
   */
  RPC_ID__Req_WifiSetMaxTxPower = 275,
  /*
   *0x114
   */
  RPC_ID__Req_WifiGetMaxTxPower = 276,
  /*
   *0x115
   */
  RPC_ID__Req_ConfigHeartbeat = 277,
  /*
   *0x116
   */
  RPC_ID__Req_WifiInit = 278,
  /*
   *0x117
   */
  RPC_ID__Req_WifiDeinit = 279,
  /*
   *0x118
   */
  RPC_ID__Req_WifiStart = 280,
  /*
   *0x119
   */
  RPC_ID__Req_WifiStop = 281,
  /*
   *0x11a
   */
  RPC_ID__Req_WifiConnect = 282,
  /*
   *0x11b
   */
  RPC_ID__Req_WifiDisconnect = 283,
  /*
   *0x11c
   */
  RPC_ID__Req_WifiSetConfig = 284,
  /*
   *0x11d
   */
  RPC_ID__Req_WifiGetConfig = 285,
  /*
   *0x11e
   */
  RPC_ID__Req_WifiScanStart = 286,
  /*
   *0x11f
   */
  RPC_ID__Req_WifiScanStop = 287,
  /*
   *0x120
   */
  RPC_ID__Req_WifiScanGetApNum = 288,
  /*
   *0x121
   */
  RPC_ID__Req_WifiScanGetApRecords = 289,
  /*
   *0x122
   */
  RPC_ID__Req_WifiClearApList = 290,
  /*
   *0x123
   */
  RPC_ID__Req_WifiRestore = 291,
  /*
   *0x124
   */
  RPC_ID__Req_WifiClearFastConnect = 292,
  /*
   *0x125
   */
  RPC_ID__Req_WifiDeauthSta = 293,
  /*
   *0x126
   */
  RPC_ID__Req_WifiStaGetApInfo = 294,
  /*
   *Req_WifiSetPs                     = 295; //0x127
   *Req_WifiGetPs                     = 296; //0x128
   */
  /*
   *0x129
   */
  RPC_ID__Req_WifiSetProtocol = 297,
  /*
   *0x12a
   */
  RPC_ID__Req_WifiGetProtocol = 298,
  /*
   *0x12b
   */
  RPC_ID__Req_WifiSetBandwidth = 299,
  /*
   *0x12c
   */
  RPC_ID__Req_WifiGetBandwidth = 300,
  /*
   *0x12d
   */
  RPC_ID__Req_WifiSetChannel = 301,
  /*
   *0x12e
   */
  RPC_ID__Req_WifiGetChannel = 302,
  /*
   *0x12f
   */
  RPC_ID__Req_WifiSetCountry = 303,
  /*
   *0x130
   */
  RPC_ID__Req_WifiGetCountry = 304,
  /*
   *  Req_WifiSetPromiscuousRxCb        = 305; //0x131
   */
  /*
   *0x131
   */
  RPC_ID__Req_WifiSetPromiscuous = 305,
  /*
   *0x132
   */
  RPC_ID__Req_WifiGetPromiscuous = 306,
  /*
   *0x133
   */
  RPC_ID__Req_WifiSetPromiscuousFilter = 307,
  /*
   *0x134
   */
  RPC_ID__Req_WifiGetPromiscuousFilter = 308,
  /*
   *0x135
   */
  RPC_ID__Req_WifiSetPromiscuousCtrlFilter = 309,
  /*
   *0x136
   */
  RPC_ID__Req_WifiGetPromiscuousCtrlFilter = 310,
  /*
   *0x137
   */
  RPC_ID__Req_WifiApGetStaList = 311,
  /*
   *0x138
   */
  RPC_ID__Req_WifiApGetStaAid = 312,
  /*
   *0x139
   */
  RPC_ID__Req_WifiSetStorage = 313,
  /*
   *0x13a
   */
  RPC_ID__Req_WifiSetVendorIe = 314,
  /*
   *  Req_WifiSetVendorIeCb             = 315; //0x13b
   */
  /*
   *0x13b
   */
  RPC_ID__Req_WifiSetEventMask = 315,
  /*
   *0x13c
   */
  RPC_ID__Req_WifiGetEventMask = 316,
  /*
   *0x13d
   */
  RPC_ID__Req_Wifi80211Tx = 317,
  /*
   *	Req_WifiSetCsiRxCb                = 318; //0x13e
   */
  /*
   *0x13e
   */
  RPC_ID__Req_WifiSetCsiConfig = 318,
  /*
   *0x13f
   */
  RPC_ID__Req_WifiSetCsi = 319,
  /*
   *0x140
   */
  RPC_ID__Req_WifiSetAntGpio = 320,
  /*
   *0x141
   */
  RPC_ID__Req_WifiGetAntGpio = 321,
  /*
   *0x142
   */
  RPC_ID__Req_WifiSetAnt = 322,
  /*
   *0x143
   */
  RPC_ID__Req_WifiGetAnt = 323,
  /*
   *0x144
   */
  RPC_ID__Req_WifiGetTsfTime = 324,
  /*
   *0x145
   */
  RPC_ID__Req_WifiSetInactiveTime = 325,
  /*
   *0x146
   */
  RPC_ID__Req_WifiGetInactiveTime = 326,
  /*
   *0x147
   */
  RPC_ID__Req_WifiStatisDump = 327,
  /*
   *0x148
   */
  RPC_ID__Req_WifiSetRssiThreshold = 328,
  /*
   *0x149
   */
  RPC_ID__Req_WifiFtmInitiateSession = 329,
  /*
   *0x14a
   */
  RPC_ID__Req_WifiFtmEndSession = 330,
  /*
   *0x14b
   */
  RPC_ID__Req_WifiFtmRespSetOffset = 331,
  /*
   *0x14c
   */
  RPC_ID__Req_WifiConfig11bRate = 332,
  /*
   *0x14d
   */
  RPC_ID__Req_WifiConnectionlessModuleSetWakeInterval = 333,
  /*
   *0x14e
   */
  RPC_ID__Req_WifiSetCountryCode = 334,
  /*
   *0x14f
   */
  RPC_ID__Req_WifiGetCountryCode = 335,
  /*
   *0x150
   */
  RPC_ID__Req_WifiConfig80211TxRate = 336,
  /*
   *0x151
   */
  RPC_ID__Req_WifiDisablePmfConfig = 337,
  /*
   *0x152
   */
  RPC_ID__Req_WifiStaGetAid = 338,
  /*
   *0x153
   */
  RPC_ID__Req_WifiStaGetNegotiatedPhymode = 339,
  /*
   *0x154
   */
  RPC_ID__Req_WifiSetDynamicCs = 340,
  /*
   *0x155
   */
  RPC_ID__Req_WifiStaGetRssi = 341,
  /*
   *0x156
   */
  RPC_ID__Req_WifiSetProtocols = 342,
  /*
   *0x157
   */
  RPC_ID__Req_WifiGetProtocols = 343,
  /*
   *0x158
   */
  RPC_ID__Req_WifiSetBandwidths = 344,
  /*
   *0x159
   */
  RPC_ID__Req_WifiGetBandwidths = 345,
  /*
   *0x15a
   */
  RPC_ID__Req_WifiSetBand = 346,
  /*
   *0x15b
   */
  RPC_ID__Req_WifiGetBand = 347,
  /*
   *0x15c
   */
  RPC_ID__Req_WifiSetBandMode = 348,
  /*
   *0x15d
   */
  RPC_ID__Req_WifiGetBandMode = 349,
  /*
   * Add new control path command response before Req_Max
   * and update Req_Max 
   */
  /*
   *0x15e
   */
  RPC_ID__Req_Max = 350,
  /*
   ** Response Msgs *
   */
  RPC_ID__Resp_Base = 512,
  RPC_ID__Resp_GetMACAddress = 513,
  RPC_ID__Resp_SetMacAddress = 514,
  RPC_ID__Resp_GetWifiMode = 515,
  RPC_ID__Resp_SetWifiMode = 516,
  RPC_ID__Resp_WifiSetPs = 526,
  RPC_ID__Resp_WifiGetPs = 527,
  RPC_ID__Resp_OTABegin = 528,
  RPC_ID__Resp_OTAWrite = 529,
  RPC_ID__Resp_OTAEnd = 530,
  RPC_ID__Resp_WifiSetMaxTxPower = 531,
  RPC_ID__Resp_WifiGetMaxTxPower = 532,
  RPC_ID__Resp_ConfigHeartbeat = 533,
  RPC_ID__Resp_WifiInit = 534,
  RPC_ID__Resp_WifiDeinit = 535,
  RPC_ID__Resp_WifiStart = 536,
  RPC_ID__Resp_WifiStop = 537,
  RPC_ID__Resp_WifiConnect = 538,
  RPC_ID__Resp_WifiDisconnect = 539,
  RPC_ID__Resp_WifiSetConfig = 540,
  RPC_ID__Resp_WifiGetConfig = 541,
  RPC_ID__Resp_WifiScanStart = 542,
  RPC_ID__Resp_WifiScanStop = 543,
  RPC_ID__Resp_WifiScanGetApNum = 544,
  RPC_ID__Resp_WifiScanGetApRecords = 545,
  RPC_ID__Resp_WifiClearApList = 546,
  RPC_ID__Resp_WifiRestore = 547,
  RPC_ID__Resp_WifiClearFastConnect = 548,
  RPC_ID__Resp_WifiDeauthSta = 549,
  RPC_ID__Resp_WifiStaGetApInfo = 550,
  /*
   *Resp_WifiSetPs                    = 551;
   *Resp_WifiGetPs                    = 552;
   */
  RPC_ID__Resp_WifiSetProtocol = 553,
  RPC_ID__Resp_WifiGetProtocol = 554,
  RPC_ID__Resp_WifiSetBandwidth = 555,
  RPC_ID__Resp_WifiGetBandwidth = 556,
  RPC_ID__Resp_WifiSetChannel = 557,
  RPC_ID__Resp_WifiGetChannel = 558,
  RPC_ID__Resp_WifiSetCountry = 559,
  RPC_ID__Resp_WifiGetCountry = 560,
  /*
   *  Resp_WifiSetPromiscuousRxCb       = 561;
   */
  RPC_ID__Resp_WifiSetPromiscuous = 561,
  RPC_ID__Resp_WifiGetPromiscuous = 562,
  RPC_ID__Resp_WifiSetPromiscuousFilter = 563,
  RPC_ID__Resp_WifiGetPromiscuousFilter = 564,
  RPC_ID__Resp_WifiSetPromiscuousCtrlFilter = 565,
  RPC_ID__Resp_WifiGetPromiscuousCtrlFilter = 566,
  RPC_ID__Resp_WifiApGetStaList = 567,
  RPC_ID__Resp_WifiApGetStaAid = 568,
  RPC_ID__Resp_WifiSetStorage = 569,
  RPC_ID__Resp_WifiSetVendorIe = 570,
  /*
   *  Resp_WifiSetVendorIeCb            = 571;
   */
  RPC_ID__Resp_WifiSetEventMask = 571,
  RPC_ID__Resp_WifiGetEventMask = 572,
  RPC_ID__Resp_Wifi80211Tx = 573,
  /*
   *	Resp_WifiSetCsiRxCb               = 573;
   */
  RPC_ID__Resp_WifiSetCsiConfig = 574,
  RPC_ID__Resp_WifiSetCsi = 575,
  RPC_ID__Resp_WifiSetAntGpio = 576,
  RPC_ID__Resp_WifiGetAntGpio = 577,
  RPC_ID__Resp_WifiSetAnt = 578,
  RPC_ID__Resp_WifiGetAnt = 579,
  RPC_ID__Resp_WifiGetTsfTime = 580,
  RPC_ID__Resp_WifiSetInactiveTime = 581,
  RPC_ID__Resp_WifiGetInactiveTime = 582,
  RPC_ID__Resp_WifiStatisDump = 583,
  RPC_ID__Resp_WifiSetRssiThreshold = 584,
  RPC_ID__Resp_WifiFtmInitiateSession = 585,
  RPC_ID__Resp_WifiFtmEndSession = 586,
  RPC_ID__Resp_WifiFtmRespSetOffset = 587,
  RPC_ID__Resp_WifiConfig11bRate = 588,
  RPC_ID__Resp_WifiConnectionlessModuleSetWakeInterval = 589,
  RPC_ID__Resp_WifiSetCountryCode = 590,
  RPC_ID__Resp_WifiGetCountryCode = 591,
  RPC_ID__Resp_WifiConfig80211TxRate = 592,
  RPC_ID__Resp_WifiDisablePmfConfig = 593,
  RPC_ID__Resp_WifiStaGetAid = 594,
  RPC_ID__Resp_WifiStaGetNegotiatedPhymode = 595,
  RPC_ID__Resp_WifiSetDynamicCs = 596,
  RPC_ID__Resp_WifiStaGetRssi = 597,
  RPC_ID__Resp_WifiSetProtocols = 598,
  RPC_ID__Resp_WifiGetProtocols = 599,
  RPC_ID__Resp_WifiSetBandwidths = 600,
  RPC_ID__Resp_WifiGetBandwidths = 601,
  RPC_ID__Resp_WifiSetBand = 602,
  RPC_ID__Resp_WifiGetBand = 603,
  RPC_ID__Resp_WifiSetBandMode = 604,
  RPC_ID__Resp_WifiGetBandMode = 605,
  /*
   * Add new control path command response before Resp_Max
   * and update Resp_Max 
   */
  RPC_ID__Resp_Max = 606,
  /*
   ** Event Msgs *
   */
  RPC_ID__Event_Base = 768,
  RPC_ID__Event_ESPInit = 769,
  RPC_ID__Event_Heartbeat = 770,
  RPC_ID__Event_AP_StaConnected = 771,
  RPC_ID__Event_AP_StaDisconnected = 772,
  RPC_ID__Event_WifiEventNoArgs = 773,
  RPC_ID__Event_StaScanDone = 774,
  RPC_ID__Event_StaConnected = 775,
  RPC_ID__Event_StaDisconnected = 776,
  /*
   * Add new control path command notification before Event_Max
   * and update Event_Max 
   */
  RPC_ID__Event_Max = 777
    PROTOBUF_C__FORCE_ENUM_TO_BE_INT_SIZE(RPC_ID)
} RpcId;

/* --- messages --- */

struct  WifiInitConfig
{
  ProtobufCMessage base;
  /*
   **< WiFi static RX buffer number 
   */
  int32_t static_rx_buf_num;
  /*
   **< WiFi dynamic RX buffer number 
   */
  int32_t dynamic_rx_buf_num;
  /*
   **< WiFi TX buffer type 
   */
  int32_t tx_buf_type;
  /*
   **< WiFi static TX buffer number 
   */
  int32_t static_tx_buf_num;
  /*
   **< WiFi dynamic TX buffer number 
   */
  int32_t dynamic_tx_buf_num;
  /*
   **< WiFi TX cache buffer number 
   */
  int32_t cache_tx_buf_num;
  /*
   **< WiFi channel state information enable flag 
   */
  int32_t csi_enable;
  /*
   **< WiFi AMPDU RX feature enable flag 
   */
  int32_t ampdu_rx_enable;
  /*
   **< WiFi AMPDU TX feature enable flag 
   */
  int32_t ampdu_tx_enable;
  /*
   **< WiFi AMSDU TX feature enable flag 
   */
  int32_t amsdu_tx_enable;
  /*
   **< WiFi NVS flash enable flag 
   */
  int32_t nvs_enable;
  /*
   **< Nano option for printf/scan family enable flag 
   */
  int32_t nano_enable;
  /*
   **< WiFi Block Ack RX window size 
   */
  int32_t rx_ba_win;
  /*
   **< WiFi Task Core ID 
   */
  int32_t wifi_task_core_id;
  /*
   **< WiFi softAP maximum length of the beacon 
   */
  int32_t beacon_max_len;
  /*
   **< WiFi management short buffer number, the minimum value is 6, the maximum value is 32 
   */
  int32_t mgmt_sbuf_num;
  /*
   **< Enables additional WiFi features and capabilities 
   */
  uint64_t feature_caps;
  /*
   **< WiFi Power Management for station at disconnected status 
   */
  protobuf_c_boolean sta_disconnected_pm;
  /*
   **< Maximum encrypt number of peers supported by espnow 
   */
  int32_t espnow_max_encrypt_num;
  /*
   **< WiFi init magic number, it should be the last field 
   */
  int32_t magic;
};
#define WIFI_INIT_CONFIG__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_init_config__descriptor) \
    , 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 }


struct  WifiCountry
{
  ProtobufCMessage base;
  /*
   **< country code string of 3 chars
   */
  ProtobufCBinaryData cc;
  /*
   **< start channel 
   */
  uint32_t schan;
  /*
   **< total channel number 
   */
  uint32_t nchan;
  /*
   **< This field is used for getting WiFi maximum transmitting power,
   *call esp_wifi_set_max_tx_power to set the maximum transmitting power. 
   */
  int32_t max_tx_power;
  /*
   **< country policy 
   */
  int32_t policy;
};
#define WIFI_COUNTRY__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_country__descriptor) \
    , {0,NULL}, 0, 0, 0, 0 }


struct  WifiActiveScanTime
{
  ProtobufCMessage base;
  /*
   **< minimum active scan time per channel, units: millisecond 
   */
  uint32_t min;
  /*
   **< maximum active scan time per channel, units: millisecond, values above 1500ms may
   *cause station to disconnect from AP and are not recommended.  
   */
  uint32_t max;
};
#define WIFI_ACTIVE_SCAN_TIME__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_active_scan_time__descriptor) \
    , 0, 0 }


struct  WifiScanTime
{
  ProtobufCMessage base;
  /*
   **< active scan time per channel, units: millisecond. 
   */
  WifiActiveScanTime *active;
  /*
   **< passive scan time per channel, units: millisecond, values above 1500ms may
   *cause station to disconnect from AP and are not recommended. 
   */
  uint32_t passive;
};
#define WIFI_SCAN_TIME__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_scan_time__descriptor) \
    , NULL, 0 }


struct  WifiScanConfig
{
  ProtobufCMessage base;
  /*
   **< SSID of AP 33char
   */
  ProtobufCBinaryData ssid;
  /*
   **< MAC address of AP 6char 
   */
  ProtobufCBinaryData bssid;
  /*
   **< channel, scan the specific channel 
   */
  uint32_t channel;
  /*
   **< enable to scan AP whose SSID is hidden 
   */
  protobuf_c_boolean show_hidden;
  /*
   **< scan type, active or passive 
   */
  int32_t scan_type;
  /*
   **< scan time per channel 
   */
  WifiScanTime *scan_time;
  /*
   **< time spent at home channel between scanning consecutive channels.
   */
  uint32_t home_chan_dwell_time;
};
#define WIFI_SCAN_CONFIG__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_scan_config__descriptor) \
    , {0,NULL}, {0,NULL}, 0, 0, 0, NULL, 0 }


struct  WifiHeApInfo
{
  ProtobufCMessage base;
  /*
   *uint8_t bss_color:6;                   **< an unsigned integer whose value is the BSS Color of the BSS corresponding to the AP * 
   *uint8_t partial_bss_color:1;           **< indicate if an AID assignment rule based on the BSS color * 
   *uint8_t bss_color_disabled:1;          **< indicate if the use of BSS color is disabled * 
   */
  /*
   * Manually have to parse for above bits 
   */
  uint32_t bitmask;
  /*
   **< in M-BSSID set, identifies the nontransmitted BSSID 
   */
  uint32_t bssid_index;
};
#define WIFI_HE_AP_INFO__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_he_ap_info__descriptor) \
    , 0, 0 }


struct  WifiApRecord
{
  ProtobufCMessage base;
  /*
   **< MAC address of AP 6char 
   */
  ProtobufCBinaryData bssid;
  /*
   **< SSID of AP 33char 
   */
  ProtobufCBinaryData ssid;
  /*
   **< channel of AP 
   */
  uint32_t primary;
  /*
   **< secondary channel of AP 
   */
  int32_t second;
  /*
   **< signal strength of AP 
   */
  int32_t rssi;
  /*
   **< authmode of AP 
   */
  int32_t authmode;
  /*
   **< pairwise cipher of AP 
   */
  int32_t pairwise_cipher;
  /*
   **< group cipher of AP 
   */
  int32_t group_cipher;
  /*
   **< antenna used to receive beacon from AP 
   */
  int32_t ant;
  /*
   *uint32_t phy_11b:1;                        **< bit: 0 flag to identify if 11b mode is enabled or not * 
   *uint32_t phy_11g:1;                        **< bit: 1 flag to identify if 11g mode is enabled or not * 
   *uint32_t phy_11n:1;                        **< bit: 2 flag to identify if 11n mode is enabled or not * 
   *uint32_t phy_lr:1;                         **< bit: 3 flag to identify if low rate is enabled or not * 
   *uint32_t wps:1;                            **< bit: 4 flag to identify if WPS is supported or not * 
   *uint32_t ftm_responder:1;                  **< bit: 5 flag to identify if FTM is supported in responder mode * 
   *uint32_t ftm_initiator:1;                  **< bit: 6 flag to identify if FTM is supported in initiator mode * 
   *uint32_t reserved:25;                      **< bit: 7..31 reserved * 
   */
  /*
   * Manually have to parse for above bits 
   */
  uint32_t bitmask;
  /*
   **< country information of AP 
   */
  WifiCountry *country;
  WifiHeApInfo *he_ap;
};
#define WIFI_AP_RECORD__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_ap_record__descriptor) \
    , {0,NULL}, {0,NULL}, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL }


struct  WifiScanThreshold
{
  ProtobufCMessage base;
  /*
   **< The minimum rssi to accept in the fast scan mode 
   */
  int32_t rssi;
  /*
   **< The weakest authmode to accept in the fast scan mode
   *Note: Incase this value is not set and password is set as per WPA2 standards(password len >= 8),
   *it will be defaulted to WPA2 and device won't connect to deprecated WEP/WPA networks.
   *Please set authmode threshold as WIFI_AUTH_WEP/WIFI_AUTH_WPA_PSK to connect to WEP/WPA networks 
   */
  int32_t authmode;
};
#define WIFI_SCAN_THRESHOLD__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_scan_threshold__descriptor) \
    , 0, 0 }


struct  WifiPmfConfig
{
  ProtobufCMessage base;
  /*
   **< Deprecated variable. Device will always connect in PMF mode if other device also advertizes PMF capability. 
   */
  protobuf_c_boolean capable;
  /*
   **< Advertizes that Protected Management Frame is required. Device will not associate to non-PMF capable devices. 
   */
  protobuf_c_boolean required;
};
#define WIFI_PMF_CONFIG__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_pmf_config__descriptor) \
    , 0, 0 }


struct  WifiApConfig
{
  ProtobufCMessage base;
  /*
   **< SSID of ESP32 soft-AP. If ssid_len field is 0, this must be a Null terminated string. Otherwise, length is set according to ssid_len. 32 char
   */
  ProtobufCBinaryData ssid;
  /*
   **< Password of ESP32 soft-AP. 64 char
   */
  ProtobufCBinaryData password;
  /*
   **< Optional length of SSID field. 
   */
  uint32_t ssid_len;
  /*
   **< Channel of ESP32 soft-AP 
   */
  uint32_t channel;
  /*
   **< Auth mode of ESP32 soft-AP. Do not support AUTH_WEP in soft-AP mode 
   */
  int32_t authmode;
  /*
   **< Broadcast SSID or not, default 0, broadcast the SSID 
   */
  uint32_t ssid_hidden;
  /*
   **< Max number of stations allowed to connect in 
   */
  uint32_t max_connection;
  /*
   **< Beacon interval which should be multiples of 100. Unit: TU(time unit, 1 TU = 1024 us). Range: 100 ~ 60000. Default value: 100 
   */
  uint32_t beacon_interval;
  /*
   **< pairwise cipher of SoftAP, group cipher will be derived using this.
   *cipher values are valid starting from WIFI_CIPHER_TYPE_TKIP, enum values before that will be considered as invalid and default cipher suites(TKIP+CCMP) will be used.
   *Valid cipher suites in softAP mode are WIFI_CIPHER_TYPE_TKIP, WIFI_CIPHER_TYPE_CCMP and WIFI_CIPHER_TYPE_TKIP_CCMP. 
   */
  int32_t pairwise_cipher;
  /*
   **< Enable FTM Responder mode 
   */
  protobuf_c_boolean ftm_responder;
  /*
   **< Configuration for Protected Management Frame 
   */
  WifiPmfConfig *pmf_cfg;
  /*
   **< Configuration for SAE PWE derivation method 
   */
  int32_t sae_pwe_h2e;
};
#define WIFI_AP_CONFIG__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_ap_config__descriptor) \
    , {0,NULL}, {0,NULL}, 0, 0, 0, 0, 0, 0, 0, 0, NULL, 0 }


struct  WifiStaConfig
{
  ProtobufCMessage base;
  /*
   **< SSID of target AP. 32char 
   */
  ProtobufCBinaryData ssid;
  /*
   **< Password of target AP. 64char 
   */
  ProtobufCBinaryData password;
  /*
   **< do all channel scan or fast scan 
   */
  int32_t scan_method;
  /*
   **< whether set MAC address of target AP or not. Generally, station_config.bssid_set needs to be 0,
   *and it needs to be 1 only when users need to check the MAC address of the AP.
   */
  protobuf_c_boolean bssid_set;
  /*
   **< MAC address of target AP 6char 
   */
  ProtobufCBinaryData bssid;
  /*
   **< channel of target AP. Set to 1~13 to scan starting from the specified channel
   *before connecting to AP. If the channel of AP is unknown, set it to 0.
   */
  uint32_t channel;
  /*
   **< Listen interval for ESP32 station to receive beacon when WIFI_PS_MAX_MODEM is set.
   *Units: AP beacon intervals. Defaults to 3 if set to 0. 
   */
  uint32_t listen_interval;
  /*
   **< sort the connect AP in the list by rssi or security mode 
   */
  int32_t sort_method;
  /*
   **< When sort_method is set, only APs which have an auth mode that is more secure
   *than the selected auth mode and a signal stronger than the minimum RSSI will be used. 
   */
  WifiScanThreshold *threshold;
  /*
   **< Configuration for Protected Management Frame. Will be advertized in RSN Capabilities in RSN IE. 
   */
  WifiPmfConfig *pmf_cfg;
  /*
   *uint32_t rm_enabled:1;                     **< Whether Radio Measurements are enabled for the connection * 
   *uint32_t btm_enabled:1;                    **< Whether BSS Transition Management is enabled for the connection * 
   *uint32_t mbo_enabled:1;                    **< Whether MBO is enabled for the connection * 
   *uint32_t ft_enabled:1;                     **< Whether FT is enabled for the connection * 
   *uint32_t owe_enabled:1;                    **< Whether OWE is enabled for the connection * 
   *uint32_t transition_disable:1;             **< Whether to enable transition disable feature * 
   *uint32_t reserved:26;                      **< Reserved for future feature set * 
   */
  uint32_t bitmask;
  /*
   **< Whether SAE hash to element is enabled 
   */
  int32_t sae_pwe_h2e;
  /*
   **< Number of connection retries station will do before moving to next AP.
   *scan_method should be set as WIFI_ALL_CHANNEL_SCAN to use this config.
   *Note: Enabling this may cause connection time to increase incase best AP doesn't behave properly. 
   */
  uint32_t failure_retry_cnt;
  /*
   *uint32_t he_dcm_set:1;                                         **< Whether DCM max.constellation for transmission and reception is set. * 
   *uint32_t he_dcm_max_constellation_tx:2;                        **< Indicate the max.constellation for DCM in TB PPDU the STA supported. 0: not supported. 1: BPSK, 2: QPSK, 3: 16-QAM. The default value is 3. * 
   *uint32_t he_dcm_max_constellation_rx:2;                        **< Indicate the max.constellation for DCM in both Data field and HE-SIG-B field the STA supported. 0: not supported. 1: BPSK, 2: QPSK, 3: 16-QAM. The default value is 3. * 
   *uint32_t he_mcs9_enabled:1;                                    **< Whether to support HE-MCS 0 to 9. The default value is 0. * 
   *uint32_t he_su_beamformee_disabled:1;                          **< Whether to disable support for operation as an SU beamformee. * 
   *uint32_t he_trig_su_bmforming_feedback_disabled:1;             **< Whether to disable support the transmission of SU feedback in an HE TB sounding sequence. * 
   *uint32_t he_trig_mu_bmforming_partial_feedback_disabled:1;     **< Whether to disable support the transmission of partial-bandwidth MU feedback in an HE TB sounding sequence. * 
   * uint32_t he_trig_cqi_feedback_disabled:1;                      **< Whether to disable support the transmission of CQI feedback in an HE TB sounding sequence. * 
   * uint32_t he_reserved:22;                                       **< Reserved for future feature set * 
   */
  uint32_t he_bitmask;
  /*
   **< Password identifier for H2E. this needs to be null terminated string. SAE_H2E_IDENTIFIER_LEN chars 
   */
  ProtobufCBinaryData sae_h2e_identifier;
};
#define WIFI_STA_CONFIG__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_sta_config__descriptor) \
    , {0,NULL}, {0,NULL}, 0, 0, {0,NULL}, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, {0,NULL} }


typedef enum {
  WIFI_CONFIG__U__NOT_SET = 0,
  WIFI_CONFIG__U_AP = 1,
  WIFI_CONFIG__U_STA = 2
    PROTOBUF_C__FORCE_ENUM_TO_BE_INT_SIZE(WIFI_CONFIG__U__CASE)
} WifiConfig__UCase;

struct  WifiConfig
{
  ProtobufCMessage base;
  WifiConfig__UCase u_case;
  union {
    /*
     **< configuration of AP 
     */
    WifiApConfig *ap;
    /*
     **< configuration of STA 
     */
    WifiStaConfig *sta;
  };
};
#define WIFI_CONFIG__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_config__descriptor) \
    , WIFI_CONFIG__U__NOT_SET, {0} }


struct  WifiStaInfo
{
  ProtobufCMessage base;
  /*
   **< mac address 6 char 
   */
  ProtobufCBinaryData mac;
  /*
   **< current average rssi of sta connected 
   */
  int32_t rssi;
  /*
   *uint32_t phy_11b:1;                        **< bit: 0 flag to identify if 11b mode is enabled or not * 
   *uint32_t phy_11g:1;                        **< bit: 1 flag to identify if 11g mode is enabled or not * 
   *uint32_t phy_11n:1;                        **< bit: 2 flag to identify if 11n mode is enabled or not * 
   *uint32_t phy_lr:1;                         **< bit: 3 flag to identify if low rate is enabled or not * 
   *uint32_t phy_11x:1;                        **< bit: 4 flag to identify identify if 11ax mode is enabled or not * 
   *uint32_t is_mesh_child:1;                  **< bit: 5 flag to identify mesh child * 
   *uint32_t reserved:26;                      **< bit: 6..31 reserved * 
   */
  uint32_t bitmask;
};
#define WIFI_STA_INFO__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_sta_info__descriptor) \
    , {0,NULL}, 0, 0 }


struct  WifiStaList
{
  ProtobufCMessage base;
  /*
   **< station list 
   */
  size_t n_sta;
  WifiStaInfo **sta;
  /*
   **< number of stations in the list (other entries are invalid) 
   */
  int32_t num;
};
#define WIFI_STA_LIST__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_sta_list__descriptor) \
    , 0,NULL, 0 }


struct  WifiPktRxCtrl
{
  ProtobufCMessage base;
  /*
   **< 8bits Received Signal Strength Indicator(RSSI) of packet. unit: dBm 
   */
  int32_t rssi;
  /*
   **< 5bits PHY rate encoding of the packet. Only valid for non HT(11bg) packet 
   */
  uint32_t rate;
  /*
   *uint32 :1;                                 **< reserved * 
   */
  /*
   **< 2bits 0: non HT(11bg) packet; 1: HT(11n) packet; 3: VHT(11ac) packet 
   */
  uint32_t sig_mode;
  /*
   *uint32 :16;                                **< reserved * 
   */
  /*
   **< 7bits Modulation Coding Scheme. If is HT(11n) packet, shows the modulation, range from 0 to 76(MSC0 ~ MCS76) 
   */
  uint32_t mcs;
  /*
   **< 1bit Channel Bandwidth of the packet. 0: 20MHz; 1: 40MHz 
   */
  uint32_t cwb;
  /*
   *uint32 :16;                                **< reserved * 
   */
  /*
   **< 1bit reserved 
   */
  uint32_t smoothing;
  /*
   **< 1bit reserved 
   */
  uint32_t not_sounding;
  /*
   *uint32 :1;                                 **< reserved * 
   */
  /*
   **< 1bit Aggregation. 0: MPDU packet; 1: AMPDU packet 
   */
  uint32_t aggregation;
  /*
   **< 2bits Space Time Block Code(STBC). 0: non STBC packet; 1: STBC packet 
   */
  uint32_t stbc;
  /*
   **< 1bit Flag is set for 11n packets which are LDPC 
   */
  uint32_t fec_coding;
  /*
   **< 1bit Short Guide Interval(SGI). 0: Long GI; 1: Short GI 
   */
  uint32_t sgi;
  /*
   **< 8bits noise floor of Radio Frequency Module(RF). unit: dBm
   */
  int32_t noise_floor;
  /*
   **< 8bits ampdu cnt 
   */
  uint32_t ampdu_cnt;
  /*
   **< 4bits primary channel on which this packet is received 
   */
  uint32_t channel;
  /*
   **< 4bits secondary channel on which this packet is received. 0: none; 1: above; 2: below 
   */
  uint32_t secondary_channel;
  /*
   *uint32 :8;                                 **< reserved * 
   */
  /*
   **< 32bit timestamp. The local time when this packet is received. It is precise only if modem sleep or light sleep is not enabled. unit: microsecond 
   */
  uint32_t timestamp;
  /*
   *uint32 :32;                                **< reserved * 
   *unsigned :32;                              **< reserved * 
   *unsigned :31;                              **< reserved * 
   */
  /*
   **< 1bit antenna number from which this packet is received. 0: WiFi antenna 0; 1: WiFi antenna 1 
   */
  uint32_t ant;
  /*
   **<  12bits length of packet including Frame Check Sequence(FCS) 
   */
  uint32_t sig_len;
  /*
   *unsigned :12;                              **< reserved * 
   */
  /*
   **< 8bits state of the packet. 0: no error; others: error numbers which are not public 
   */
  uint32_t rx_state;
};
#define WIFI_PKT_RX_CTRL__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_pkt_rx_ctrl__descriptor) \
    , 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 }


struct  WifiPromiscuousPkt
{
  ProtobufCMessage base;
  /*
   **< metadata header 
   */
  WifiPktRxCtrl *rx_ctrl;
  /*
   **< Note: variable length. Data or management payload. Length of payload is described by rx_ctrl.sig_len. Type of content determined by packet type argument of callback. 
   */
  ProtobufCBinaryData payload;
};
#define WIFI_PROMISCUOUS_PKT__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_promiscuous_pkt__descriptor) \
    , NULL, {0,NULL} }


struct  WifiPromiscuousFilter
{
  ProtobufCMessage base;
  /*
   **< OR of one or more filter values WIFI_PROMIS_FILTER_* 
   */
  uint32_t filter_mask;
};
#define WIFI_PROMISCUOUS_FILTER__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_promiscuous_filter__descriptor) \
    , 0 }


struct  WifiCsiConfig
{
  ProtobufCMessage base;
  /*
   **< enable to receive legacy long training field(lltf) data. Default enabled 
   */
  protobuf_c_boolean lltf_en;
  /*
   **< enable to receive HT long training field(htltf) data. Default enabled 
   */
  protobuf_c_boolean htltf_en;
  /*
   **< enable to receive space time block code HT long training field(stbc-htltf2) data. Default enabled 
   */
  protobuf_c_boolean stbc_htltf2_en;
  /*
   **< enable to generate htlft data by averaging lltf and ht_ltf data when receiving HT packet. Otherwise, use ht_ltf data directly. Default enabled 
   */
  protobuf_c_boolean ltf_merge_en;
  /*
   **< enable to turn on channel filter to smooth adjacent sub-carrier. Disable it to keep independence of adjacent sub-carrier. Default enabled 
   */
  protobuf_c_boolean channel_filter_en;
  /*
   **< manually scale the CSI data by left shifting or automatically scale the CSI data.
   *If set true, please set the shift bits. false: automatically. true: manually. Default false 
   */
  protobuf_c_boolean manu_scale;
  /*
   **< manually left shift bits of the scale of the CSI data. The range of the left shift bits is 0~15 
   */
  uint32_t shift;
};
#define WIFI_CSI_CONFIG__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_csi_config__descriptor) \
    , 0, 0, 0, 0, 0, 0, 0 }


struct  WifiCsiInfo
{
  ProtobufCMessage base;
  /*
   **< received packet radio metadata header of the CSI data 
   */
  WifiPktRxCtrl *rx_ctrl;
  /*
   **< 6bits source MAC address of the CSI data 
   */
  ProtobufCBinaryData mac;
  /*
   **< 6bits destination MAC address of the CSI data 
   */
  ProtobufCBinaryData dmac;
  /*
   **< first four bytes of the CSI data is invalid or not 
   */
  protobuf_c_boolean first_word_invalid;
  /*
   **< Note: variable length. buffer of CSI data 
   */
  ProtobufCBinaryData buf;
  /*
   **< length of CSI data 
   */
  uint32_t len;
};
#define WIFI_CSI_INFO__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_csi_info__descriptor) \
    , NULL, {0,NULL}, {0,NULL}, 0, {0,NULL}, 0 }


struct  WifiAntGpio
{
  ProtobufCMessage base;
  /*
   **< 1bit Whether this GPIO is connected to external antenna switch 
   */
  uint32_t gpio_select;
  /*
   **< 7bits The GPIO number that connects to external antenna switch 
   */
  uint32_t gpio_num;
};
#define WIFI_ANT_GPIO__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_ant_gpio__descriptor) \
    , 0, 0 }


struct  WifiAntGpioConfig
{
  ProtobufCMessage base;
  /*
   **< The configurations of GPIOs that connect to external antenna switch 
   */
  size_t n_gpio_cfgs;
  WifiAntGpio **gpio_cfgs;
};
#define WIFI_ANT_GPIO_CONFIG__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_ant_gpio_config__descriptor) \
    , 0,NULL }


struct  WifiAntConfig
{
  ProtobufCMessage base;
  /*
   **< WiFi antenna mode for receiving 
   */
  int32_t rx_ant_mode;
  /*
   **< Default antenna mode for receiving, it's ignored if rx_ant_mode is not WIFI_ANT_MODE_AUTO 
   */
  int32_t rx_ant_default;
  /*
   **< WiFi antenna mode for transmission, it can be set to WIFI_ANT_MODE_AUTO only if rx_ant_mode is set to WIFI_ANT_MODE_AUTO 
   */
  int32_t tx_ant_mode;
  /*
   **< 4bits Index (in antenna GPIO configuration) of enabled WIFI_ANT_MODE_ANT0 
   */
  uint32_t enabled_ant0;
  /*
   **< 4bits Index (in antenna GPIO configuration) of enabled WIFI_ANT_MODE_ANT1 
   */
  uint32_t enabled_ant1;
};
#define WIFI_ANT_CONFIG__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_ant_config__descriptor) \
    , 0, 0, 0, 0, 0 }


struct  WifiActionTxReq
{
  ProtobufCMessage base;
  /*
   **< WiFi interface to send request to 
   */
  int32_t ifx;
  /*
   **< 6bits Destination MAC address 
   */
  ProtobufCBinaryData dest_mac;
  /*
   **< Indicates no ack required 
   */
  protobuf_c_boolean no_ack;
  /*
   *TODO
   *wifi_action_rx_cb_t rx_cb;                 **< Rx Callback to receive any response * 
   */
  /*
   **< Length of the appended Data 
   */
  uint32_t data_len;
  /*
   **< note: variable length. Appended Data payload 
   */
  ProtobufCBinaryData data;
};
#define WIFI_ACTION_TX_REQ__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_action_tx_req__descriptor) \
    , 0, {0,NULL}, 0, 0, {0,NULL} }


struct  WifiFtmInitiatorCfg
{
  ProtobufCMessage base;
  /*
   **< 6bits MAC address of the FTM Responder 
   */
  ProtobufCBinaryData resp_mac;
  /*
   **< Primary channel of the FTM Responder 
   */
  uint32_t channel;
  /*
   **< No. of FTM frames requested in terms of 4 or 8 bursts (allowed values - 0(No pref), 16, 24, 32, 64) 
   */
  uint32_t frm_count;
  /*
   **< Requested time period between consecutive FTM bursts in 100's of milliseconds (0 - No pref) 
   */
  uint32_t burst_period;
};
#define WIFI_FTM_INITIATOR_CFG__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_ftm_initiator_cfg__descriptor) \
    , {0,NULL}, 0, 0, 0 }


struct  WifiEventStaScanDone
{
  ProtobufCMessage base;
  /*
   **< status of scanning APs: 0 — success, 1 - failure 
   */
  uint32_t status;
  /*
   **< number of scan results 
   */
  uint32_t number;
  /*
   **< scan sequence number, used for block scan 
   */
  uint32_t scan_id;
};
#define WIFI_EVENT_STA_SCAN_DONE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_event_sta_scan_done__descriptor) \
    , 0, 0, 0 }


struct  WifiEventStaConnected
{
  ProtobufCMessage base;
  /*
   **< 32bytes SSID of connected AP 
   */
  ProtobufCBinaryData ssid;
  /*
   **< SSID length of connected AP 
   */
  uint32_t ssid_len;
  /*
   **< 6bytes BSSID of connected AP
   */
  ProtobufCBinaryData bssid;
  /*
   **< channel of connected AP
   */
  uint32_t channel;
  /*
   **< authentication mode used by AP
   */
  int32_t authmode;
  /*
   **< authentication id assigned by the connected AP
   */
  int32_t aid;
};
#define WIFI_EVENT_STA_CONNECTED__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_event_sta_connected__descriptor) \
    , {0,NULL}, 0, {0,NULL}, 0, 0, 0 }


struct  WifiEventStaDisconnected
{
  ProtobufCMessage base;
  /*
   **< SSID of disconnected AP 
   */
  ProtobufCBinaryData ssid;
  /*
   **< SSID length of disconnected AP 
   */
  uint32_t ssid_len;
  /*
   **< BSSID of disconnected AP 
   */
  ProtobufCBinaryData bssid;
  /*
   **< reason of disconnection 
   */
  uint32_t reason;
  /*
   **< rssi of disconnection 
   */
  int32_t rssi;
};
#define WIFI_EVENT_STA_DISCONNECTED__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_event_sta_disconnected__descriptor) \
    , {0,NULL}, 0, {0,NULL}, 0, 0 }


struct  WifiEventStaAuthmodeChange
{
  ProtobufCMessage base;
  /*
   **< the old auth mode of AP 
   */
  int32_t old_mode;
  /*
   **< the new auth mode of AP 
   */
  int32_t new_mode;
};
#define WIFI_EVENT_STA_AUTHMODE_CHANGE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_event_sta_authmode_change__descriptor) \
    , 0, 0 }


struct  WifiEventStaWpsErPin
{
  ProtobufCMessage base;
  /*
   **< 8bytes PIN code of station in enrollee mode 
   */
  ProtobufCBinaryData pin_code;
};
#define WIFI_EVENT_STA_WPS_ER_PIN__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_event_sta_wps_er_pin__descriptor) \
    , {0,NULL} }


struct  ApCred
{
  ProtobufCMessage base;
  /*
   **< 32bytes SSID of AP 
   */
  ProtobufCBinaryData ssid;
  /*
   **< 64bytes Passphrase for the AP 
   */
  ProtobufCBinaryData passphrase;
};
#define AP_CRED__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&ap_cred__descriptor) \
    , {0,NULL}, {0,NULL} }


struct  WifiEventStaWpsErSuccess
{
  ProtobufCMessage base;
  /*
   **< Number of AP credentials received 
   */
  uint32_t ap_cred_cnt;
  /*
   **< All AP credentials received from WPS handshake 
   */
  size_t n_ap_creds;
  ApCred **ap_creds;
};
#define WIFI_EVENT_STA_WPS_ER_SUCCESS__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_event_sta_wps_er_success__descriptor) \
    , 0, 0,NULL }


/*
 ** Argument structure for WIFI_EVENT_AP_PROBEREQRECVED event 
 */
struct  WifiEventApProbeReqRx
{
  ProtobufCMessage base;
  /*
   **< Received probe request signal strength 
   */
  int32_t rssi;
  /*
   **< MAC address of the station which send probe request 
   */
  uint32_t mac;
};
#define WIFI_EVENT_AP_PROBE_REQ_RX__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_event_ap_probe_req_rx__descriptor) \
    , 0, 0 }


/*
 ** Argument structure for WIFI_EVENT_STA_BSS_RSSI_LOW event 
 */
struct  WifiEventBssRssiLow
{
  ProtobufCMessage base;
  /*
   **< RSSI value of bss 
   */
  int32_t rssi;
};
#define WIFI_EVENT_BSS_RSSI_LOW__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_event_bss_rssi_low__descriptor) \
    , 0 }


struct  WifiFtmReportEntry
{
  ProtobufCMessage base;
  /*
   * *< Dialog Token of the FTM frame 
   */
  uint32_t dlog_token;
  /*
   * *< RSSI of the FTM frame received 
   */
  int32_t rssi;
  /*
   * *< Round Trip Time in pSec with a peer 
   */
  uint32_t rtt;
  /*
   * TODO: uint32 is supported by proto? 
   */
  /*
   * *< Time of departure of FTM frame from FTM Responder in pSec 
   */
  uint64_t t1;
  /*
   * *< Time of arrival of FTM frame at FTM Initiator in pSec 
   */
  uint64_t t2;
  /*
   * *< Time of departure of ACK from FTM Initiator in pSec 
   */
  uint64_t t3;
  /*
   * *< Time of arrival of ACK at FTM Responder in pSec 
   */
  uint64_t t4;
};
#define WIFI_FTM_REPORT_ENTRY__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_ftm_report_entry__descriptor) \
    , 0, 0, 0, 0, 0, 0, 0 }


struct  WifiEventFtmReport
{
  ProtobufCMessage base;
  /*
   * *< 6bytes MAC address of the FTM Peer 
   */
  ProtobufCBinaryData peer_mac;
  /*
   * *< Status of the FTM operation 
   */
  int32_t status;
  /*
   * *< Raw average Round-Trip-Time with peer in Nano-Seconds 
   */
  uint32_t rtt_raw;
  /*
   * *< Estimated Round-Trip-Time with peer in Nano-Seconds 
   */
  uint32_t rtt_est;
  /*
   * *< Estimated one-way distance in Centi-Meters 
   */
  uint32_t dist_est;
  /*
   * *< Note var len Pointer to FTM Report with multiple entries, should be freed after use 
   */
  size_t n_ftm_report_data;
  WifiFtmReportEntry **ftm_report_data;
  /*
   * *< Number of entries in the FTM Report data 
   */
  uint32_t ftm_report_num_entries;
};
#define WIFI_EVENT_FTM_REPORT__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_event_ftm_report__descriptor) \
    , {0,NULL}, 0, 0, 0, 0, 0,NULL, 0 }


struct  WifiEventActionTxStatus
{
  ProtobufCMessage base;
  /*
   **< WiFi interface to send request to 
   */
  int32_t ifx;
  /*
   **< Context to identify the request 
   */
  uint32_t context;
  /*
   **< 6bytes Destination MAC address 
   */
  ProtobufCBinaryData da;
  /*
   **< Status of the operation 
   */
  uint32_t status;
};
#define WIFI_EVENT_ACTION_TX_STATUS__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_event_action_tx_status__descriptor) \
    , 0, 0, {0,NULL}, 0 }


struct  WifiEventRocDone
{
  ProtobufCMessage base;
  /*
   **< Context to identify the request 
   */
  uint32_t context;
};
#define WIFI_EVENT_ROC_DONE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_event_roc_done__descriptor) \
    , 0 }


struct  WifiEventApWpsRgPin
{
  ProtobufCMessage base;
  /*
   **< 8bytes PIN code of station in enrollee mode 
   */
  ProtobufCBinaryData pin_code;
};
#define WIFI_EVENT_AP_WPS_RG_PIN__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_event_ap_wps_rg_pin__descriptor) \
    , {0,NULL} }


struct  WifiEventApWpsRgFailReason
{
  ProtobufCMessage base;
  /*
   **< WPS failure reason wps_fail_reason_t 
   */
  int32_t reason;
  /*
   **< 6bytes Enrollee mac address 
   */
  ProtobufCBinaryData peer_macaddr;
};
#define WIFI_EVENT_AP_WPS_RG_FAIL_REASON__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_event_ap_wps_rg_fail_reason__descriptor) \
    , 0, {0,NULL} }


struct  WifiEventApWpsRgSuccess
{
  ProtobufCMessage base;
  /*
   **< 6bytes Enrollee mac address 
   */
  ProtobufCBinaryData peer_macaddr;
};
#define WIFI_EVENT_AP_WPS_RG_SUCCESS__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_event_ap_wps_rg_success__descriptor) \
    , {0,NULL} }


struct  WifiProtocols
{
  ProtobufCMessage base;
  /*
   **< Represents 2.4 GHz protocol, support 802.11b or 802.11g or 802.11n or 802.11ax or LR mode 
   */
  uint32_t ghz_2g;
  /*
   **< Represents 5 GHz protocol, support 802.11a or 802.11n or 802.11ac or 802.11ax 
   */
  uint32_t ghz_5g;
};
#define WIFI_PROTOCOLS__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_protocols__descriptor) \
    , 0, 0 }


struct  WifiBandwidths
{
  ProtobufCMessage base;
  /*
   * Represents 2.4 GHz bandwidth 
   */
  uint32_t ghz_2g;
  /*
   * Represents 5 GHz bandwidth 
   */
  uint32_t ghz_5g;
};
#define WIFI_BANDWIDTHS__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&wifi_bandwidths__descriptor) \
    , 0, 0 }


struct  ConnectedSTAList
{
  ProtobufCMessage base;
  ProtobufCBinaryData mac;
  int32_t rssi;
};
#define CONNECTED_STALIST__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&connected_stalist__descriptor) \
    , {0,NULL}, 0 }


/*
 ** Req/Resp structure *
 */
struct  RpcReqGetMacAddress
{
  ProtobufCMessage base;
  int32_t mode;
};
#define RPC__REQ__GET_MAC_ADDRESS__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__get_mac_address__descriptor) \
    , 0 }


struct  RpcRespGetMacAddress
{
  ProtobufCMessage base;
  ProtobufCBinaryData mac;
  int32_t resp;
};
#define RPC__RESP__GET_MAC_ADDRESS__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__get_mac_address__descriptor) \
    , {0,NULL}, 0 }


struct  RpcReqGetMode
{
  ProtobufCMessage base;
};
#define RPC__REQ__GET_MODE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__get_mode__descriptor) \
     }


struct  RpcRespGetMode
{
  ProtobufCMessage base;
  int32_t mode;
  int32_t resp;
};
#define RPC__RESP__GET_MODE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__get_mode__descriptor) \
    , 0, 0 }


struct  RpcReqSetMode
{
  ProtobufCMessage base;
  int32_t mode;
};
#define RPC__REQ__SET_MODE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__set_mode__descriptor) \
    , 0 }


struct  RpcRespSetMode
{
  ProtobufCMessage base;
  int32_t resp;
};
#define RPC__RESP__SET_MODE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__set_mode__descriptor) \
    , 0 }


struct  RpcReqGetPs
{
  ProtobufCMessage base;
};
#define RPC__REQ__GET_PS__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__get_ps__descriptor) \
     }


struct  RpcRespGetPs
{
  ProtobufCMessage base;
  int32_t resp;
  int32_t type;
};
#define RPC__RESP__GET_PS__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__get_ps__descriptor) \
    , 0, 0 }


struct  RpcReqSetPs
{
  ProtobufCMessage base;
  int32_t type;
};
#define RPC__REQ__SET_PS__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__set_ps__descriptor) \
    , 0 }


struct  RpcRespSetPs
{
  ProtobufCMessage base;
  int32_t resp;
};
#define RPC__RESP__SET_PS__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__set_ps__descriptor) \
    , 0 }


struct  RpcReqSetMacAddress
{
  ProtobufCMessage base;
  ProtobufCBinaryData mac;
  int32_t mode;
};
#define RPC__REQ__SET_MAC_ADDRESS__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__set_mac_address__descriptor) \
    , {0,NULL}, 0 }


struct  RpcRespSetMacAddress
{
  ProtobufCMessage base;
  int32_t resp;
};
#define RPC__RESP__SET_MAC_ADDRESS__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__set_mac_address__descriptor) \
    , 0 }


struct  RpcReqOTABegin
{
  ProtobufCMessage base;
};
#define RPC__REQ__OTABEGIN__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__otabegin__descriptor) \
     }


struct  RpcRespOTABegin
{
  ProtobufCMessage base;
  int32_t resp;
};
#define RPC__RESP__OTABEGIN__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__otabegin__descriptor) \
    , 0 }


struct  RpcReqOTAWrite
{
  ProtobufCMessage base;
  ProtobufCBinaryData ota_data;
};
#define RPC__REQ__OTAWRITE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__otawrite__descriptor) \
    , {0,NULL} }


struct  RpcRespOTAWrite
{
  ProtobufCMessage base;
  int32_t resp;
};
#define RPC__RESP__OTAWRITE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__otawrite__descriptor) \
    , 0 }


struct  RpcReqOTAEnd
{
  ProtobufCMessage base;
};
#define RPC__REQ__OTAEND__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__otaend__descriptor) \
     }


struct  RpcRespOTAEnd
{
  ProtobufCMessage base;
  int32_t resp;
};
#define RPC__RESP__OTAEND__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__otaend__descriptor) \
    , 0 }


struct  RpcReqWifiSetMaxTxPower
{
  ProtobufCMessage base;
  int32_t power;
};
#define RPC__REQ__WIFI_SET_MAX_TX_POWER__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_set_max_tx_power__descriptor) \
    , 0 }


struct  RpcRespWifiSetMaxTxPower
{
  ProtobufCMessage base;
  int32_t resp;
};
#define RPC__RESP__WIFI_SET_MAX_TX_POWER__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_set_max_tx_power__descriptor) \
    , 0 }


struct  RpcReqWifiGetMaxTxPower
{
  ProtobufCMessage base;
};
#define RPC__REQ__WIFI_GET_MAX_TX_POWER__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_get_max_tx_power__descriptor) \
     }


struct  RpcRespWifiGetMaxTxPower
{
  ProtobufCMessage base;
  int32_t power;
  int32_t resp;
};
#define RPC__RESP__WIFI_GET_MAX_TX_POWER__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_get_max_tx_power__descriptor) \
    , 0, 0 }


struct  RpcReqConfigHeartbeat
{
  ProtobufCMessage base;
  protobuf_c_boolean enable;
  int32_t duration;
};
#define RPC__REQ__CONFIG_HEARTBEAT__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__config_heartbeat__descriptor) \
    , 0, 0 }


struct  RpcRespConfigHeartbeat
{
  ProtobufCMessage base;
  int32_t resp;
};
#define RPC__RESP__CONFIG_HEARTBEAT__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__config_heartbeat__descriptor) \
    , 0 }


struct  RpcReqWifiInit
{
  ProtobufCMessage base;
  WifiInitConfig *cfg;
};
#define RPC__REQ__WIFI_INIT__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_init__descriptor) \
    , NULL }


struct  RpcRespWifiInit
{
  ProtobufCMessage base;
  int32_t resp;
};
#define RPC__RESP__WIFI_INIT__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_init__descriptor) \
    , 0 }


struct  RpcReqWifiDeinit
{
  ProtobufCMessage base;
};
#define RPC__REQ__WIFI_DEINIT__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_deinit__descriptor) \
     }


struct  RpcRespWifiDeinit
{
  ProtobufCMessage base;
  int32_t resp;
};
#define RPC__RESP__WIFI_DEINIT__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_deinit__descriptor) \
    , 0 }


struct  RpcReqWifiSetConfig
{
  ProtobufCMessage base;
  int32_t iface;
  WifiConfig *cfg;
};
#define RPC__REQ__WIFI_SET_CONFIG__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_set_config__descriptor) \
    , 0, NULL }


struct  RpcRespWifiSetConfig
{
  ProtobufCMessage base;
  int32_t resp;
};
#define RPC__RESP__WIFI_SET_CONFIG__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_set_config__descriptor) \
    , 0 }


struct  RpcReqWifiGetConfig
{
  ProtobufCMessage base;
  int32_t iface;
};
#define RPC__REQ__WIFI_GET_CONFIG__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_get_config__descriptor) \
    , 0 }


struct  RpcRespWifiGetConfig
{
  ProtobufCMessage base;
  int32_t resp;
  int32_t iface;
  WifiConfig *cfg;
};
#define RPC__RESP__WIFI_GET_CONFIG__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_get_config__descriptor) \
    , 0, 0, NULL }


struct  RpcReqWifiConnect
{
  ProtobufCMessage base;
};
#define RPC__REQ__WIFI_CONNECT__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_connect__descriptor) \
     }


struct  RpcRespWifiConnect
{
  ProtobufCMessage base;
  int32_t resp;
};
#define RPC__RESP__WIFI_CONNECT__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_connect__descriptor) \
    , 0 }


struct  RpcReqWifiDisconnect
{
  ProtobufCMessage base;
};
#define RPC__REQ__WIFI_DISCONNECT__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_disconnect__descriptor) \
     }


struct  RpcRespWifiDisconnect
{
  ProtobufCMessage base;
  int32_t resp;
};
#define RPC__RESP__WIFI_DISCONNECT__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_disconnect__descriptor) \
    , 0 }


struct  RpcReqWifiStart
{
  ProtobufCMessage base;
};
#define RPC__REQ__WIFI_START__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_start__descriptor) \
     }


struct  RpcRespWifiStart
{
  ProtobufCMessage base;
  int32_t resp;
};
#define RPC__RESP__WIFI_START__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_start__descriptor) \
    , 0 }


struct  RpcReqWifiStop
{
  ProtobufCMessage base;
};
#define RPC__REQ__WIFI_STOP__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_stop__descriptor) \
     }


struct  RpcRespWifiStop
{
  ProtobufCMessage base;
  int32_t resp;
};
#define RPC__RESP__WIFI_STOP__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_stop__descriptor) \
    , 0 }


struct  RpcReqWifiScanStart
{
  ProtobufCMessage base;
  WifiScanConfig *config;
  protobuf_c_boolean block;
  int32_t config_set;
};
#define RPC__REQ__WIFI_SCAN_START__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_scan_start__descriptor) \
    , NULL, 0, 0 }


struct  RpcRespWifiScanStart
{
  ProtobufCMessage base;
  int32_t resp;
};
#define RPC__RESP__WIFI_SCAN_START__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_scan_start__descriptor) \
    , 0 }


struct  RpcReqWifiScanStop
{
  ProtobufCMessage base;
};
#define RPC__REQ__WIFI_SCAN_STOP__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_scan_stop__descriptor) \
     }


struct  RpcRespWifiScanStop
{
  ProtobufCMessage base;
  int32_t resp;
};
#define RPC__RESP__WIFI_SCAN_STOP__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_scan_stop__descriptor) \
    , 0 }


struct  RpcReqWifiScanGetApNum
{
  ProtobufCMessage base;
};
#define RPC__REQ__WIFI_SCAN_GET_AP_NUM__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_scan_get_ap_num__descriptor) \
     }


struct  RpcRespWifiScanGetApNum
{
  ProtobufCMessage base;
  int32_t resp;
  int32_t number;
};
#define RPC__RESP__WIFI_SCAN_GET_AP_NUM__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_scan_get_ap_num__descriptor) \
    , 0, 0 }


struct  RpcReqWifiScanGetApRecords
{
  ProtobufCMessage base;
  int32_t number;
};
#define RPC__REQ__WIFI_SCAN_GET_AP_RECORDS__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_scan_get_ap_records__descriptor) \
    , 0 }


struct  RpcRespWifiScanGetApRecords
{
  ProtobufCMessage base;
  int32_t resp;
  int32_t number;
  size_t n_ap_records;
  WifiApRecord **ap_records;
};
#define RPC__RESP__WIFI_SCAN_GET_AP_RECORDS__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_scan_get_ap_records__descriptor) \
    , 0, 0, 0,NULL }


struct  RpcReqWifiClearApList
{
  ProtobufCMessage base;
};
#define RPC__REQ__WIFI_CLEAR_AP_LIST__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_clear_ap_list__descriptor) \
     }


struct  RpcRespWifiClearApList
{
  ProtobufCMessage base;
  int32_t resp;
};
#define RPC__RESP__WIFI_CLEAR_AP_LIST__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_clear_ap_list__descriptor) \
    , 0 }


struct  RpcReqWifiRestore
{
  ProtobufCMessage base;
};
#define RPC__REQ__WIFI_RESTORE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_restore__descriptor) \
     }


struct  RpcRespWifiRestore
{
  ProtobufCMessage base;
  int32_t resp;
};
#define RPC__RESP__WIFI_RESTORE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_restore__descriptor) \
    , 0 }


struct  RpcReqWifiClearFastConnect
{
  ProtobufCMessage base;
};
#define RPC__REQ__WIFI_CLEAR_FAST_CONNECT__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_clear_fast_connect__descriptor) \
     }


struct  RpcRespWifiClearFastConnect
{
  ProtobufCMessage base;
  int32_t resp;
};
#define RPC__RESP__WIFI_CLEAR_FAST_CONNECT__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_clear_fast_connect__descriptor) \
    , 0 }


struct  RpcReqWifiDeauthSta
{
  ProtobufCMessage base;
  int32_t aid;
};
#define RPC__REQ__WIFI_DEAUTH_STA__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_deauth_sta__descriptor) \
    , 0 }


struct  RpcRespWifiDeauthSta
{
  ProtobufCMessage base;
  int32_t resp;
  int32_t aid;
};
#define RPC__RESP__WIFI_DEAUTH_STA__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_deauth_sta__descriptor) \
    , 0, 0 }


struct  RpcReqWifiStaGetApInfo
{
  ProtobufCMessage base;
};
#define RPC__REQ__WIFI_STA_GET_AP_INFO__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_sta_get_ap_info__descriptor) \
     }


struct  RpcRespWifiStaGetApInfo
{
  ProtobufCMessage base;
  int32_t resp;
  WifiApRecord *ap_records;
};
#define RPC__RESP__WIFI_STA_GET_AP_INFO__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_sta_get_ap_info__descriptor) \
    , 0, NULL }


struct  RpcReqWifiSetProtocol
{
  ProtobufCMessage base;
  int32_t ifx;
  int32_t protocol_bitmap;
};
#define RPC__REQ__WIFI_SET_PROTOCOL__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_set_protocol__descriptor) \
    , 0, 0 }


struct  RpcRespWifiSetProtocol
{
  ProtobufCMessage base;
  int32_t resp;
};
#define RPC__RESP__WIFI_SET_PROTOCOL__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_set_protocol__descriptor) \
    , 0 }


struct  RpcReqWifiGetProtocol
{
  ProtobufCMessage base;
  int32_t ifx;
};
#define RPC__REQ__WIFI_GET_PROTOCOL__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_get_protocol__descriptor) \
    , 0 }


struct  RpcRespWifiGetProtocol
{
  ProtobufCMessage base;
  int32_t resp;
  int32_t protocol_bitmap;
};
#define RPC__RESP__WIFI_GET_PROTOCOL__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_get_protocol__descriptor) \
    , 0, 0 }


struct  RpcReqWifiSetBandwidth
{
  ProtobufCMessage base;
  int32_t ifx;
  int32_t bw;
};
#define RPC__REQ__WIFI_SET_BANDWIDTH__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_set_bandwidth__descriptor) \
    , 0, 0 }


struct  RpcRespWifiSetBandwidth
{
  ProtobufCMessage base;
  int32_t resp;
};
#define RPC__RESP__WIFI_SET_BANDWIDTH__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_set_bandwidth__descriptor) \
    , 0 }


struct  RpcReqWifiGetBandwidth
{
  ProtobufCMessage base;
  int32_t ifx;
};
#define RPC__REQ__WIFI_GET_BANDWIDTH__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_get_bandwidth__descriptor) \
    , 0 }


struct  RpcRespWifiGetBandwidth
{
  ProtobufCMessage base;
  int32_t resp;
  int32_t bw;
};
#define RPC__RESP__WIFI_GET_BANDWIDTH__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_get_bandwidth__descriptor) \
    , 0, 0 }


struct  RpcReqWifiSetChannel
{
  ProtobufCMessage base;
  int32_t primary;
  int32_t second;
};
#define RPC__REQ__WIFI_SET_CHANNEL__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_set_channel__descriptor) \
    , 0, 0 }


struct  RpcRespWifiSetChannel
{
  ProtobufCMessage base;
  int32_t resp;
};
#define RPC__RESP__WIFI_SET_CHANNEL__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_set_channel__descriptor) \
    , 0 }


struct  RpcReqWifiGetChannel
{
  ProtobufCMessage base;
};
#define RPC__REQ__WIFI_GET_CHANNEL__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_get_channel__descriptor) \
     }


struct  RpcRespWifiGetChannel
{
  ProtobufCMessage base;
  int32_t resp;
  int32_t primary;
  int32_t second;
};
#define RPC__RESP__WIFI_GET_CHANNEL__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_get_channel__descriptor) \
    , 0, 0, 0 }


struct  RpcReqWifiSetStorage
{
  ProtobufCMessage base;
  int32_t storage;
};
#define RPC__REQ__WIFI_SET_STORAGE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_set_storage__descriptor) \
    , 0 }


struct  RpcRespWifiSetStorage
{
  ProtobufCMessage base;
  int32_t resp;
};
#define RPC__RESP__WIFI_SET_STORAGE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_set_storage__descriptor) \
    , 0 }


struct  RpcReqWifiSetCountryCode
{
  ProtobufCMessage base;
  ProtobufCBinaryData country;
  protobuf_c_boolean ieee80211d_enabled;
};
#define RPC__REQ__WIFI_SET_COUNTRY_CODE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_set_country_code__descriptor) \
    , {0,NULL}, 0 }


struct  RpcRespWifiSetCountryCode
{
  ProtobufCMessage base;
  int32_t resp;
};
#define RPC__RESP__WIFI_SET_COUNTRY_CODE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_set_country_code__descriptor) \
    , 0 }


struct  RpcReqWifiGetCountryCode
{
  ProtobufCMessage base;
};
#define RPC__REQ__WIFI_GET_COUNTRY_CODE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_get_country_code__descriptor) \
     }


struct  RpcRespWifiGetCountryCode
{
  ProtobufCMessage base;
  int32_t resp;
  ProtobufCBinaryData country;
};
#define RPC__RESP__WIFI_GET_COUNTRY_CODE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_get_country_code__descriptor) \
    , 0, {0,NULL} }


struct  RpcReqWifiSetCountry
{
  ProtobufCMessage base;
  WifiCountry *country;
};
#define RPC__REQ__WIFI_SET_COUNTRY__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_set_country__descriptor) \
    , NULL }


struct  RpcRespWifiSetCountry
{
  ProtobufCMessage base;
  int32_t resp;
};
#define RPC__RESP__WIFI_SET_COUNTRY__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_set_country__descriptor) \
    , 0 }


struct  RpcReqWifiGetCountry
{
  ProtobufCMessage base;
};
#define RPC__REQ__WIFI_GET_COUNTRY__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_get_country__descriptor) \
     }


struct  RpcRespWifiGetCountry
{
  ProtobufCMessage base;
  int32_t resp;
  WifiCountry *country;
};
#define RPC__RESP__WIFI_GET_COUNTRY__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_get_country__descriptor) \
    , 0, NULL }


struct  RpcReqWifiApGetStaList
{
  ProtobufCMessage base;
};
#define RPC__REQ__WIFI_AP_GET_STA_LIST__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_ap_get_sta_list__descriptor) \
     }


struct  RpcRespWifiApGetStaList
{
  ProtobufCMessage base;
  int32_t resp;
  WifiStaList *sta_list;
};
#define RPC__RESP__WIFI_AP_GET_STA_LIST__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_ap_get_sta_list__descriptor) \
    , 0, NULL }


struct  RpcReqWifiApGetStaAid
{
  ProtobufCMessage base;
  ProtobufCBinaryData mac;
};
#define RPC__REQ__WIFI_AP_GET_STA_AID__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_ap_get_sta_aid__descriptor) \
    , {0,NULL} }


struct  RpcRespWifiApGetStaAid
{
  ProtobufCMessage base;
  int32_t resp;
  uint32_t aid;
};
#define RPC__RESP__WIFI_AP_GET_STA_AID__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_ap_get_sta_aid__descriptor) \
    , 0, 0 }


struct  RpcReqWifiStaGetRssi
{
  ProtobufCMessage base;
};
#define RPC__REQ__WIFI_STA_GET_RSSI__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_sta_get_rssi__descriptor) \
     }


struct  RpcRespWifiStaGetRssi
{
  ProtobufCMessage base;
  int32_t resp;
  int32_t rssi;
};
#define RPC__RESP__WIFI_STA_GET_RSSI__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_sta_get_rssi__descriptor) \
    , 0, 0 }


struct  RpcReqWifiStaGetAid
{
  ProtobufCMessage base;
};
#define RPC__REQ__WIFI_STA_GET_AID__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_sta_get_aid__descriptor) \
     }


struct  RpcRespWifiStaGetAid
{
  ProtobufCMessage base;
  int32_t resp;
  uint32_t aid;
};
#define RPC__RESP__WIFI_STA_GET_AID__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_sta_get_aid__descriptor) \
    , 0, 0 }


struct  RpcReqWifiSetProtocols
{
  ProtobufCMessage base;
  int32_t ifx;
  WifiProtocols *protocols;
};
#define RPC__REQ__WIFI_SET_PROTOCOLS__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_set_protocols__descriptor) \
    , 0, NULL }


struct  RpcRespWifiSetProtocols
{
  ProtobufCMessage base;
  int32_t resp;
  uint32_t ifx;
};
#define RPC__RESP__WIFI_SET_PROTOCOLS__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_set_protocols__descriptor) \
    , 0, 0 }


struct  RpcReqWifiGetProtocols
{
  ProtobufCMessage base;
  int32_t ifx;
};
#define RPC__REQ__WIFI_GET_PROTOCOLS__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_get_protocols__descriptor) \
    , 0 }


struct  RpcRespWifiGetProtocols
{
  ProtobufCMessage base;
  int32_t resp;
  int32_t ifx;
  WifiProtocols *protocols;
};
#define RPC__RESP__WIFI_GET_PROTOCOLS__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_get_protocols__descriptor) \
    , 0, 0, NULL }


struct  RpcReqWifiSetBandwidths
{
  ProtobufCMessage base;
  int32_t ifx;
  WifiBandwidths *bandwidths;
};
#define RPC__REQ__WIFI_SET_BANDWIDTHS__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_set_bandwidths__descriptor) \
    , 0, NULL }


struct  RpcRespWifiSetBandwidths
{
  ProtobufCMessage base;
  int32_t resp;
  int32_t ifx;
};
#define RPC__RESP__WIFI_SET_BANDWIDTHS__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_set_bandwidths__descriptor) \
    , 0, 0 }


struct  RpcReqWifiGetBandwidths
{
  ProtobufCMessage base;
  int32_t ifx;
};
#define RPC__REQ__WIFI_GET_BANDWIDTHS__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_get_bandwidths__descriptor) \
    , 0 }


struct  RpcRespWifiGetBandwidths
{
  ProtobufCMessage base;
  int32_t resp;
  int32_t ifx;
  WifiBandwidths *bandwidths;
};
#define RPC__RESP__WIFI_GET_BANDWIDTHS__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_get_bandwidths__descriptor) \
    , 0, 0, NULL }


struct  RpcReqWifiSetBand
{
  ProtobufCMessage base;
  uint32_t band;
};
#define RPC__REQ__WIFI_SET_BAND__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_set_band__descriptor) \
    , 0 }


struct  RpcRespWifiSetBand
{
  ProtobufCMessage base;
  int32_t resp;
};
#define RPC__RESP__WIFI_SET_BAND__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_set_band__descriptor) \
    , 0 }


struct  RpcReqWifiGetBand
{
  ProtobufCMessage base;
};
#define RPC__REQ__WIFI_GET_BAND__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_get_band__descriptor) \
     }


struct  RpcRespWifiGetBand
{
  ProtobufCMessage base;
  int32_t resp;
  uint32_t band;
};
#define RPC__RESP__WIFI_GET_BAND__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_get_band__descriptor) \
    , 0, 0 }


struct  RpcReqWifiSetBandMode
{
  ProtobufCMessage base;
  uint32_t bandmode;
};
#define RPC__REQ__WIFI_SET_BAND_MODE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_set_band_mode__descriptor) \
    , 0 }


struct  RpcRespWifiSetBandMode
{
  ProtobufCMessage base;
  int32_t resp;
};
#define RPC__RESP__WIFI_SET_BAND_MODE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_set_band_mode__descriptor) \
    , 0 }


struct  RpcReqWifiGetBandMode
{
  ProtobufCMessage base;
};
#define RPC__REQ__WIFI_GET_BAND_MODE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__req__wifi_get_band_mode__descriptor) \
     }


struct  RpcRespWifiGetBandMode
{
  ProtobufCMessage base;
  int32_t resp;
  uint32_t bandmode;
};
#define RPC__RESP__WIFI_GET_BAND_MODE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__resp__wifi_get_band_mode__descriptor) \
    , 0, 0 }


struct  RpcEventWifiEventNoArgs
{
  ProtobufCMessage base;
  int32_t resp;
  int32_t event_id;
};
#define RPC__EVENT__WIFI_EVENT_NO_ARGS__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__event__wifi_event_no_args__descriptor) \
    , 0, 0 }


struct  RpcEventESPInit
{
  ProtobufCMessage base;
  ProtobufCBinaryData init_data;
};
#define RPC__EVENT__ESPINIT__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__event__espinit__descriptor) \
    , {0,NULL} }


struct  RpcEventHeartbeat
{
  ProtobufCMessage base;
  int32_t hb_num;
};
#define RPC__EVENT__HEARTBEAT__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__event__heartbeat__descriptor) \
    , 0 }


struct  RpcEventAPStaDisconnected
{
  ProtobufCMessage base;
  int32_t resp;
  ProtobufCBinaryData mac;
  uint32_t aid;
  protobuf_c_boolean is_mesh_child;
  uint32_t reason;
};
#define RPC__EVENT__AP__STA_DISCONNECTED__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__event__ap__sta_disconnected__descriptor) \
    , 0, {0,NULL}, 0, 0, 0 }


struct  RpcEventAPStaConnected
{
  ProtobufCMessage base;
  int32_t resp;
  ProtobufCBinaryData mac;
  uint32_t aid;
  protobuf_c_boolean is_mesh_child;
};
#define RPC__EVENT__AP__STA_CONNECTED__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__event__ap__sta_connected__descriptor) \
    , 0, {0,NULL}, 0, 0 }


struct  RpcEventStaScanDone
{
  ProtobufCMessage base;
  int32_t resp;
  WifiEventStaScanDone *scan_done;
};
#define RPC__EVENT__STA_SCAN_DONE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__event__sta_scan_done__descriptor) \
    , 0, NULL }


struct  RpcEventStaConnected
{
  ProtobufCMessage base;
  int32_t resp;
  WifiEventStaConnected *sta_connected;
};
#define RPC__EVENT__STA_CONNECTED__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__event__sta_connected__descriptor) \
    , 0, NULL }


struct  RpcEventStaDisconnected
{
  ProtobufCMessage base;
  int32_t resp;
  WifiEventStaDisconnected *sta_disconnected;
};
#define RPC__EVENT__STA_DISCONNECTED__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__event__sta_disconnected__descriptor) \
    , 0, NULL }


typedef enum {
  RPC__PAYLOAD__NOT_SET = 0,
  RPC__PAYLOAD_REQ_GET_MAC_ADDRESS = 257,
  RPC__PAYLOAD_REQ_SET_MAC_ADDRESS = 258,
  RPC__PAYLOAD_REQ_GET_WIFI_MODE = 259,
  RPC__PAYLOAD_REQ_SET_WIFI_MODE = 260,
  RPC__PAYLOAD_REQ_WIFI_SET_PS = 270,
  RPC__PAYLOAD_REQ_WIFI_GET_PS = 271,
  RPC__PAYLOAD_REQ_OTA_BEGIN = 272,
  RPC__PAYLOAD_REQ_OTA_WRITE = 273,
  RPC__PAYLOAD_REQ_OTA_END = 274,
  RPC__PAYLOAD_REQ_SET_WIFI_MAX_TX_POWER = 275,
  RPC__PAYLOAD_REQ_GET_WIFI_MAX_TX_POWER = 276,
  RPC__PAYLOAD_REQ_CONFIG_HEARTBEAT = 277,
  RPC__PAYLOAD_REQ_WIFI_INIT = 278,
  RPC__PAYLOAD_REQ_WIFI_DEINIT = 279,
  RPC__PAYLOAD_REQ_WIFI_START = 280,
  RPC__PAYLOAD_REQ_WIFI_STOP = 281,
  RPC__PAYLOAD_REQ_WIFI_CONNECT = 282,
  RPC__PAYLOAD_REQ_WIFI_DISCONNECT = 283,
  RPC__PAYLOAD_REQ_WIFI_SET_CONFIG = 284,
  RPC__PAYLOAD_REQ_WIFI_GET_CONFIG = 285,
  RPC__PAYLOAD_REQ_WIFI_SCAN_START = 286,
  RPC__PAYLOAD_REQ_WIFI_SCAN_STOP = 287,
  RPC__PAYLOAD_REQ_WIFI_SCAN_GET_AP_NUM = 288,
  RPC__PAYLOAD_REQ_WIFI_SCAN_GET_AP_RECORDS = 289,
  RPC__PAYLOAD_REQ_WIFI_CLEAR_AP_LIST = 290,
  RPC__PAYLOAD_REQ_WIFI_RESTORE = 291,
  RPC__PAYLOAD_REQ_WIFI_CLEAR_FAST_CONNECT = 292,
  RPC__PAYLOAD_REQ_WIFI_DEAUTH_STA = 293,
  RPC__PAYLOAD_REQ_WIFI_STA_GET_AP_INFO = 294,
  RPC__PAYLOAD_REQ_WIFI_SET_PROTOCOL = 297,
  RPC__PAYLOAD_REQ_WIFI_GET_PROTOCOL = 298,
  RPC__PAYLOAD_REQ_WIFI_SET_BANDWIDTH = 299,
  RPC__PAYLOAD_REQ_WIFI_GET_BANDWIDTH = 300,
  RPC__PAYLOAD_REQ_WIFI_SET_CHANNEL = 301,
  RPC__PAYLOAD_REQ_WIFI_GET_CHANNEL = 302,
  RPC__PAYLOAD_REQ_WIFI_SET_COUNTRY = 303,
  RPC__PAYLOAD_REQ_WIFI_GET_COUNTRY = 304,
  RPC__PAYLOAD_REQ_WIFI_AP_GET_STA_LIST = 311,
  RPC__PAYLOAD_REQ_WIFI_AP_GET_STA_AID = 312,
  RPC__PAYLOAD_REQ_WIFI_SET_STORAGE = 313,
  RPC__PAYLOAD_REQ_WIFI_SET_COUNTRY_CODE = 334,
  RPC__PAYLOAD_REQ_WIFI_GET_COUNTRY_CODE = 335,
  RPC__PAYLOAD_REQ_WIFI_STA_GET_AID = 338,
  RPC__PAYLOAD_REQ_WIFI_STA_GET_RSSI = 341,
  RPC__PAYLOAD_REQ_WIFI_SET_PROTOCOLS = 342,
  RPC__PAYLOAD_REQ_WIFI_GET_PROTOCOLS = 343,
  RPC__PAYLOAD_REQ_WIFI_SET_BANDWIDTHS = 344,
  RPC__PAYLOAD_REQ_WIFI_GET_BANDWIDTHS = 345,
  RPC__PAYLOAD_REQ_WIFI_SET_BAND = 346,
  RPC__PAYLOAD_REQ_WIFI_GET_BAND = 347,
  RPC__PAYLOAD_REQ_WIFI_SET_BANDMODE = 348,
  RPC__PAYLOAD_REQ_WIFI_GET_BANDMODE = 349,
  RPC__PAYLOAD_RESP_GET_MAC_ADDRESS = 513,
  RPC__PAYLOAD_RESP_SET_MAC_ADDRESS = 514,
  RPC__PAYLOAD_RESP_GET_WIFI_MODE = 515,
  RPC__PAYLOAD_RESP_SET_WIFI_MODE = 516,
  RPC__PAYLOAD_RESP_WIFI_SET_PS = 526,
  RPC__PAYLOAD_RESP_WIFI_GET_PS = 527,
  RPC__PAYLOAD_RESP_OTA_BEGIN = 528,
  RPC__PAYLOAD_RESP_OTA_WRITE = 529,
  RPC__PAYLOAD_RESP_OTA_END = 530,
  RPC__PAYLOAD_RESP_SET_WIFI_MAX_TX_POWER = 531,
  RPC__PAYLOAD_RESP_GET_WIFI_MAX_TX_POWER = 532,
  RPC__PAYLOAD_RESP_CONFIG_HEARTBEAT = 533,
  RPC__PAYLOAD_RESP_WIFI_INIT = 534,
  RPC__PAYLOAD_RESP_WIFI_DEINIT = 535,
  RPC__PAYLOAD_RESP_WIFI_START = 536,
  RPC__PAYLOAD_RESP_WIFI_STOP = 537,
  RPC__PAYLOAD_RESP_WIFI_CONNECT = 538,
  RPC__PAYLOAD_RESP_WIFI_DISCONNECT = 539,
  RPC__PAYLOAD_RESP_WIFI_SET_CONFIG = 540,
  RPC__PAYLOAD_RESP_WIFI_GET_CONFIG = 541,
  RPC__PAYLOAD_RESP_WIFI_SCAN_START = 542,
  RPC__PAYLOAD_RESP_WIFI_SCAN_STOP = 543,
  RPC__PAYLOAD_RESP_WIFI_SCAN_GET_AP_NUM = 544,
  RPC__PAYLOAD_RESP_WIFI_SCAN_GET_AP_RECORDS = 545,
  RPC__PAYLOAD_RESP_WIFI_CLEAR_AP_LIST = 546,
  RPC__PAYLOAD_RESP_WIFI_RESTORE = 547,
  RPC__PAYLOAD_RESP_WIFI_CLEAR_FAST_CONNECT = 548,
  RPC__PAYLOAD_RESP_WIFI_DEAUTH_STA = 549,
  RPC__PAYLOAD_RESP_WIFI_STA_GET_AP_INFO = 550,
  RPC__PAYLOAD_RESP_WIFI_SET_PROTOCOL = 553,
  RPC__PAYLOAD_RESP_WIFI_GET_PROTOCOL = 554,
  RPC__PAYLOAD_RESP_WIFI_SET_BANDWIDTH = 555,
  RPC__PAYLOAD_RESP_WIFI_GET_BANDWIDTH = 556,
  RPC__PAYLOAD_RESP_WIFI_SET_CHANNEL = 557,
  RPC__PAYLOAD_RESP_WIFI_GET_CHANNEL = 558,
  RPC__PAYLOAD_RESP_WIFI_SET_COUNTRY = 559,
  RPC__PAYLOAD_RESP_WIFI_GET_COUNTRY = 560,
  RPC__PAYLOAD_RESP_WIFI_AP_GET_STA_LIST = 567,
  RPC__PAYLOAD_RESP_WIFI_AP_GET_STA_AID = 568,
  RPC__PAYLOAD_RESP_WIFI_SET_STORAGE = 569,
  RPC__PAYLOAD_RESP_WIFI_SET_COUNTRY_CODE = 590,
  RPC__PAYLOAD_RESP_WIFI_GET_COUNTRY_CODE = 591,
  RPC__PAYLOAD_RESP_WIFI_STA_GET_AID = 594,
  RPC__PAYLOAD_RESP_WIFI_STA_GET_RSSI = 597,
  RPC__PAYLOAD_RESP_WIFI_SET_PROTOCOLS = 598,
  RPC__PAYLOAD_RESP_WIFI_GET_PROTOCOLS = 599,
  RPC__PAYLOAD_RESP_WIFI_SET_BANDWIDTHS = 600,
  RPC__PAYLOAD_RESP_WIFI_GET_BANDWIDTHS = 601,
  RPC__PAYLOAD_RESP_WIFI_SET_BAND = 602,
  RPC__PAYLOAD_RESP_WIFI_GET_BAND = 603,
  RPC__PAYLOAD_RESP_WIFI_SET_BANDMODE = 604,
  RPC__PAYLOAD_RESP_WIFI_GET_BANDMODE = 605,
  RPC__PAYLOAD_EVENT_ESP_INIT = 769,
  RPC__PAYLOAD_EVENT_HEARTBEAT = 770,
  RPC__PAYLOAD_EVENT_AP_STA_CONNECTED = 771,
  RPC__PAYLOAD_EVENT_AP_STA_DISCONNECTED = 772,
  RPC__PAYLOAD_EVENT_WIFI_EVENT_NO_ARGS = 773,
  RPC__PAYLOAD_EVENT_STA_SCAN_DONE = 774,
  RPC__PAYLOAD_EVENT_STA_CONNECTED = 775,
  RPC__PAYLOAD_EVENT_STA_DISCONNECTED = 776
    PROTOBUF_C__FORCE_ENUM_TO_BE_INT_SIZE(RPC__PAYLOAD__CASE)
} Rpc__PayloadCase;

struct  Rpc
{
  ProtobufCMessage base;
  /*
   * msg_type could be req, resp or Event 
   */
  RpcType msg_type;
  /*
   * msg id 
   */
  RpcId msg_id;
  /*
   * UID of message 
   */
  uint32_t uid;
  Rpc__PayloadCase payload_case;
  union {
    /*
     ** Requests *
     */
    RpcReqGetMacAddress *req_get_mac_address;
    RpcReqSetMacAddress *req_set_mac_address;
    RpcReqGetMode *req_get_wifi_mode;
    RpcReqSetMode *req_set_wifi_mode;
    RpcReqSetPs *req_wifi_set_ps;
    RpcReqGetPs *req_wifi_get_ps;
    RpcReqOTABegin *req_ota_begin;
    RpcReqOTAWrite *req_ota_write;
    RpcReqOTAEnd *req_ota_end;
    RpcReqWifiSetMaxTxPower *req_set_wifi_max_tx_power;
    RpcReqWifiGetMaxTxPower *req_get_wifi_max_tx_power;
    RpcReqConfigHeartbeat *req_config_heartbeat;
    RpcReqWifiInit *req_wifi_init;
    RpcReqWifiDeinit *req_wifi_deinit;
    RpcReqWifiStart *req_wifi_start;
    RpcReqWifiStop *req_wifi_stop;
    RpcReqWifiConnect *req_wifi_connect;
    RpcReqWifiDisconnect *req_wifi_disconnect;
    RpcReqWifiSetConfig *req_wifi_set_config;
    RpcReqWifiGetConfig *req_wifi_get_config;
    RpcReqWifiScanStart *req_wifi_scan_start;
    RpcReqWifiScanStop *req_wifi_scan_stop;
    RpcReqWifiScanGetApNum *req_wifi_scan_get_ap_num;
    RpcReqWifiScanGetApRecords *req_wifi_scan_get_ap_records;
    RpcReqWifiClearApList *req_wifi_clear_ap_list;
    RpcReqWifiRestore *req_wifi_restore;
    RpcReqWifiClearFastConnect *req_wifi_clear_fast_connect;
    RpcReqWifiDeauthSta *req_wifi_deauth_sta;
    RpcReqWifiStaGetApInfo *req_wifi_sta_get_ap_info;
    RpcReqWifiSetProtocol *req_wifi_set_protocol;
    RpcReqWifiGetProtocol *req_wifi_get_protocol;
    RpcReqWifiSetBandwidth *req_wifi_set_bandwidth;
    RpcReqWifiGetBandwidth *req_wifi_get_bandwidth;
    RpcReqWifiSetChannel *req_wifi_set_channel;
    RpcReqWifiGetChannel *req_wifi_get_channel;
    RpcReqWifiSetCountry *req_wifi_set_country;
    RpcReqWifiGetCountry *req_wifi_get_country;
    RpcReqWifiApGetStaList *req_wifi_ap_get_sta_list;
    RpcReqWifiApGetStaAid *req_wifi_ap_get_sta_aid;
    RpcReqWifiSetStorage *req_wifi_set_storage;
    RpcReqWifiSetCountryCode *req_wifi_set_country_code;
    RpcReqWifiGetCountryCode *req_wifi_get_country_code;
    RpcReqWifiStaGetAid *req_wifi_sta_get_aid;
    RpcReqWifiStaGetRssi *req_wifi_sta_get_rssi;
    RpcReqWifiSetProtocols *req_wifi_set_protocols;
    RpcReqWifiGetProtocols *req_wifi_get_protocols;
    RpcReqWifiSetBandwidths *req_wifi_set_bandwidths;
    RpcReqWifiGetBandwidths *req_wifi_get_bandwidths;
    RpcReqWifiSetBand *req_wifi_set_band;
    RpcReqWifiGetBand *req_wifi_get_band;
    RpcReqWifiSetBandMode *req_wifi_set_bandmode;
    RpcReqWifiGetBandMode *req_wifi_get_bandmode;
    /*
     ** Responses *
     */
    RpcRespGetMacAddress *resp_get_mac_address;
    RpcRespSetMacAddress *resp_set_mac_address;
    RpcRespGetMode *resp_get_wifi_mode;
    RpcRespSetMode *resp_set_wifi_mode;
    RpcRespSetPs *resp_wifi_set_ps;
    RpcRespGetPs *resp_wifi_get_ps;
    RpcRespOTABegin *resp_ota_begin;
    RpcRespOTAWrite *resp_ota_write;
    RpcRespOTAEnd *resp_ota_end;
    RpcRespWifiSetMaxTxPower *resp_set_wifi_max_tx_power;
    RpcRespWifiGetMaxTxPower *resp_get_wifi_max_tx_power;
    RpcRespConfigHeartbeat *resp_config_heartbeat;
    RpcRespWifiInit *resp_wifi_init;
    RpcRespWifiDeinit *resp_wifi_deinit;
    RpcRespWifiStart *resp_wifi_start;
    RpcRespWifiStop *resp_wifi_stop;
    RpcRespWifiConnect *resp_wifi_connect;
    RpcRespWifiDisconnect *resp_wifi_disconnect;
    RpcRespWifiSetConfig *resp_wifi_set_config;
    RpcRespWifiGetConfig *resp_wifi_get_config;
    RpcRespWifiScanStart *resp_wifi_scan_start;
    RpcRespWifiScanStop *resp_wifi_scan_stop;
    RpcRespWifiScanGetApNum *resp_wifi_scan_get_ap_num;
    RpcRespWifiScanGetApRecords *resp_wifi_scan_get_ap_records;
    RpcRespWifiClearApList *resp_wifi_clear_ap_list;
    RpcRespWifiRestore *resp_wifi_restore;
    RpcRespWifiClearFastConnect *resp_wifi_clear_fast_connect;
    RpcRespWifiDeauthSta *resp_wifi_deauth_sta;
    RpcRespWifiStaGetApInfo *resp_wifi_sta_get_ap_info;
    RpcRespWifiSetProtocol *resp_wifi_set_protocol;
    RpcRespWifiGetProtocol *resp_wifi_get_protocol;
    RpcRespWifiSetBandwidth *resp_wifi_set_bandwidth;
    RpcRespWifiGetBandwidth *resp_wifi_get_bandwidth;
    RpcRespWifiSetChannel *resp_wifi_set_channel;
    RpcRespWifiGetChannel *resp_wifi_get_channel;
    RpcRespWifiSetCountry *resp_wifi_set_country;
    RpcRespWifiGetCountry *resp_wifi_get_country;
    RpcRespWifiApGetStaList *resp_wifi_ap_get_sta_list;
    RpcRespWifiApGetStaAid *resp_wifi_ap_get_sta_aid;
    RpcRespWifiSetStorage *resp_wifi_set_storage;
    RpcRespWifiSetCountryCode *resp_wifi_set_country_code;
    RpcRespWifiGetCountryCode *resp_wifi_get_country_code;
    RpcRespWifiStaGetAid *resp_wifi_sta_get_aid;
    RpcRespWifiStaGetRssi *resp_wifi_sta_get_rssi;
    RpcRespWifiSetProtocols *resp_wifi_set_protocols;
    RpcRespWifiGetProtocols *resp_wifi_get_protocols;
    RpcRespWifiSetBandwidths *resp_wifi_set_bandwidths;
    RpcRespWifiGetBandwidths *resp_wifi_get_bandwidths;
    RpcRespWifiSetBand *resp_wifi_set_band;
    RpcRespWifiGetBand *resp_wifi_get_band;
    RpcRespWifiSetBandMode *resp_wifi_set_bandmode;
    RpcRespWifiGetBandMode *resp_wifi_get_bandmode;
    /*
     ** Notifications *
     */
    RpcEventESPInit *event_esp_init;
    RpcEventHeartbeat *event_heartbeat;
    RpcEventAPStaConnected *event_ap_sta_connected;
    RpcEventAPStaDisconnected *event_ap_sta_disconnected;
    RpcEventWifiEventNoArgs *event_wifi_event_no_args;
    RpcEventStaScanDone *event_sta_scan_done;
    RpcEventStaConnected *event_sta_connected;
    RpcEventStaDisconnected *event_sta_disconnected;
  };
};
#define RPC__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rpc__descriptor) \
    , RPC_TYPE__MsgType_Invalid, RPC_ID__MsgId_Invalid, 0, RPC__PAYLOAD__NOT_SET, {0} }


/* WifiInitConfig methods */
void   wifi_init_config__init
                     (WifiInitConfig         *message);
size_t wifi_init_config__get_packed_size
                     (const WifiInitConfig   *message);
size_t wifi_init_config__pack
                     (const WifiInitConfig   *message,
                      uint8_t             *out);
size_t wifi_init_config__pack_to_buffer
                     (const WifiInitConfig   *message,
                      ProtobufCBuffer     *buffer);
WifiInitConfig *
       wifi_init_config__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_init_config__free_unpacked
                     (WifiInitConfig *message,
                      ProtobufCAllocator *allocator);
/* WifiCountry methods */
void   wifi_country__init
                     (WifiCountry         *message);
size_t wifi_country__get_packed_size
                     (const WifiCountry   *message);
size_t wifi_country__pack
                     (const WifiCountry   *message,
                      uint8_t             *out);
size_t wifi_country__pack_to_buffer
                     (const WifiCountry   *message,
                      ProtobufCBuffer     *buffer);
WifiCountry *
       wifi_country__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_country__free_unpacked
                     (WifiCountry *message,
                      ProtobufCAllocator *allocator);
/* WifiActiveScanTime methods */
void   wifi_active_scan_time__init
                     (WifiActiveScanTime         *message);
size_t wifi_active_scan_time__get_packed_size
                     (const WifiActiveScanTime   *message);
size_t wifi_active_scan_time__pack
                     (const WifiActiveScanTime   *message,
                      uint8_t             *out);
size_t wifi_active_scan_time__pack_to_buffer
                     (const WifiActiveScanTime   *message,
                      ProtobufCBuffer     *buffer);
WifiActiveScanTime *
       wifi_active_scan_time__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_active_scan_time__free_unpacked
                     (WifiActiveScanTime *message,
                      ProtobufCAllocator *allocator);
/* WifiScanTime methods */
void   wifi_scan_time__init
                     (WifiScanTime         *message);
size_t wifi_scan_time__get_packed_size
                     (const WifiScanTime   *message);
size_t wifi_scan_time__pack
                     (const WifiScanTime   *message,
                      uint8_t             *out);
size_t wifi_scan_time__pack_to_buffer
                     (const WifiScanTime   *message,
                      ProtobufCBuffer     *buffer);
WifiScanTime *
       wifi_scan_time__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_scan_time__free_unpacked
                     (WifiScanTime *message,
                      ProtobufCAllocator *allocator);
/* WifiScanConfig methods */
void   wifi_scan_config__init
                     (WifiScanConfig         *message);
size_t wifi_scan_config__get_packed_size
                     (const WifiScanConfig   *message);
size_t wifi_scan_config__pack
                     (const WifiScanConfig   *message,
                      uint8_t             *out);
size_t wifi_scan_config__pack_to_buffer
                     (const WifiScanConfig   *message,
                      ProtobufCBuffer     *buffer);
WifiScanConfig *
       wifi_scan_config__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_scan_config__free_unpacked
                     (WifiScanConfig *message,
                      ProtobufCAllocator *allocator);
/* WifiHeApInfo methods */
void   wifi_he_ap_info__init
                     (WifiHeApInfo         *message);
size_t wifi_he_ap_info__get_packed_size
                     (const WifiHeApInfo   *message);
size_t wifi_he_ap_info__pack
                     (const WifiHeApInfo   *message,
                      uint8_t             *out);
size_t wifi_he_ap_info__pack_to_buffer
                     (const WifiHeApInfo   *message,
                      ProtobufCBuffer     *buffer);
WifiHeApInfo *
       wifi_he_ap_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_he_ap_info__free_unpacked
                     (WifiHeApInfo *message,
                      ProtobufCAllocator *allocator);
/* WifiApRecord methods */
void   wifi_ap_record__init
                     (WifiApRecord         *message);
size_t wifi_ap_record__get_packed_size
                     (const WifiApRecord   *message);
size_t wifi_ap_record__pack
                     (const WifiApRecord   *message,
                      uint8_t             *out);
size_t wifi_ap_record__pack_to_buffer
                     (const WifiApRecord   *message,
                      ProtobufCBuffer     *buffer);
WifiApRecord *
       wifi_ap_record__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_ap_record__free_unpacked
                     (WifiApRecord *message,
                      ProtobufCAllocator *allocator);
/* WifiScanThreshold methods */
void   wifi_scan_threshold__init
                     (WifiScanThreshold         *message);
size_t wifi_scan_threshold__get_packed_size
                     (const WifiScanThreshold   *message);
size_t wifi_scan_threshold__pack
                     (const WifiScanThreshold   *message,
                      uint8_t             *out);
size_t wifi_scan_threshold__pack_to_buffer
                     (const WifiScanThreshold   *message,
                      ProtobufCBuffer     *buffer);
WifiScanThreshold *
       wifi_scan_threshold__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_scan_threshold__free_unpacked
                     (WifiScanThreshold *message,
                      ProtobufCAllocator *allocator);
/* WifiPmfConfig methods */
void   wifi_pmf_config__init
                     (WifiPmfConfig         *message);
size_t wifi_pmf_config__get_packed_size
                     (const WifiPmfConfig   *message);
size_t wifi_pmf_config__pack
                     (const WifiPmfConfig   *message,
                      uint8_t             *out);
size_t wifi_pmf_config__pack_to_buffer
                     (const WifiPmfConfig   *message,
                      ProtobufCBuffer     *buffer);
WifiPmfConfig *
       wifi_pmf_config__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_pmf_config__free_unpacked
                     (WifiPmfConfig *message,
                      ProtobufCAllocator *allocator);
/* WifiApConfig methods */
void   wifi_ap_config__init
                     (WifiApConfig         *message);
size_t wifi_ap_config__get_packed_size
                     (const WifiApConfig   *message);
size_t wifi_ap_config__pack
                     (const WifiApConfig   *message,
                      uint8_t             *out);
size_t wifi_ap_config__pack_to_buffer
                     (const WifiApConfig   *message,
                      ProtobufCBuffer     *buffer);
WifiApConfig *
       wifi_ap_config__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_ap_config__free_unpacked
                     (WifiApConfig *message,
                      ProtobufCAllocator *allocator);
/* WifiStaConfig methods */
void   wifi_sta_config__init
                     (WifiStaConfig         *message);
size_t wifi_sta_config__get_packed_size
                     (const WifiStaConfig   *message);
size_t wifi_sta_config__pack
                     (const WifiStaConfig   *message,
                      uint8_t             *out);
size_t wifi_sta_config__pack_to_buffer
                     (const WifiStaConfig   *message,
                      ProtobufCBuffer     *buffer);
WifiStaConfig *
       wifi_sta_config__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_sta_config__free_unpacked
                     (WifiStaConfig *message,
                      ProtobufCAllocator *allocator);
/* WifiConfig methods */
void   wifi_config__init
                     (WifiConfig         *message);
size_t wifi_config__get_packed_size
                     (const WifiConfig   *message);
size_t wifi_config__pack
                     (const WifiConfig   *message,
                      uint8_t             *out);
size_t wifi_config__pack_to_buffer
                     (const WifiConfig   *message,
                      ProtobufCBuffer     *buffer);
WifiConfig *
       wifi_config__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_config__free_unpacked
                     (WifiConfig *message,
                      ProtobufCAllocator *allocator);
/* WifiStaInfo methods */
void   wifi_sta_info__init
                     (WifiStaInfo         *message);
size_t wifi_sta_info__get_packed_size
                     (const WifiStaInfo   *message);
size_t wifi_sta_info__pack
                     (const WifiStaInfo   *message,
                      uint8_t             *out);
size_t wifi_sta_info__pack_to_buffer
                     (const WifiStaInfo   *message,
                      ProtobufCBuffer     *buffer);
WifiStaInfo *
       wifi_sta_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_sta_info__free_unpacked
                     (WifiStaInfo *message,
                      ProtobufCAllocator *allocator);
/* WifiStaList methods */
void   wifi_sta_list__init
                     (WifiStaList         *message);
size_t wifi_sta_list__get_packed_size
                     (const WifiStaList   *message);
size_t wifi_sta_list__pack
                     (const WifiStaList   *message,
                      uint8_t             *out);
size_t wifi_sta_list__pack_to_buffer
                     (const WifiStaList   *message,
                      ProtobufCBuffer     *buffer);
WifiStaList *
       wifi_sta_list__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_sta_list__free_unpacked
                     (WifiStaList *message,
                      ProtobufCAllocator *allocator);
/* WifiPktRxCtrl methods */
void   wifi_pkt_rx_ctrl__init
                     (WifiPktRxCtrl         *message);
size_t wifi_pkt_rx_ctrl__get_packed_size
                     (const WifiPktRxCtrl   *message);
size_t wifi_pkt_rx_ctrl__pack
                     (const WifiPktRxCtrl   *message,
                      uint8_t             *out);
size_t wifi_pkt_rx_ctrl__pack_to_buffer
                     (const WifiPktRxCtrl   *message,
                      ProtobufCBuffer     *buffer);
WifiPktRxCtrl *
       wifi_pkt_rx_ctrl__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_pkt_rx_ctrl__free_unpacked
                     (WifiPktRxCtrl *message,
                      ProtobufCAllocator *allocator);
/* WifiPromiscuousPkt methods */
void   wifi_promiscuous_pkt__init
                     (WifiPromiscuousPkt         *message);
size_t wifi_promiscuous_pkt__get_packed_size
                     (const WifiPromiscuousPkt   *message);
size_t wifi_promiscuous_pkt__pack
                     (const WifiPromiscuousPkt   *message,
                      uint8_t             *out);
size_t wifi_promiscuous_pkt__pack_to_buffer
                     (const WifiPromiscuousPkt   *message,
                      ProtobufCBuffer     *buffer);
WifiPromiscuousPkt *
       wifi_promiscuous_pkt__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_promiscuous_pkt__free_unpacked
                     (WifiPromiscuousPkt *message,
                      ProtobufCAllocator *allocator);
/* WifiPromiscuousFilter methods */
void   wifi_promiscuous_filter__init
                     (WifiPromiscuousFilter         *message);
size_t wifi_promiscuous_filter__get_packed_size
                     (const WifiPromiscuousFilter   *message);
size_t wifi_promiscuous_filter__pack
                     (const WifiPromiscuousFilter   *message,
                      uint8_t             *out);
size_t wifi_promiscuous_filter__pack_to_buffer
                     (const WifiPromiscuousFilter   *message,
                      ProtobufCBuffer     *buffer);
WifiPromiscuousFilter *
       wifi_promiscuous_filter__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_promiscuous_filter__free_unpacked
                     (WifiPromiscuousFilter *message,
                      ProtobufCAllocator *allocator);
/* WifiCsiConfig methods */
void   wifi_csi_config__init
                     (WifiCsiConfig         *message);
size_t wifi_csi_config__get_packed_size
                     (const WifiCsiConfig   *message);
size_t wifi_csi_config__pack
                     (const WifiCsiConfig   *message,
                      uint8_t             *out);
size_t wifi_csi_config__pack_to_buffer
                     (const WifiCsiConfig   *message,
                      ProtobufCBuffer     *buffer);
WifiCsiConfig *
       wifi_csi_config__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_csi_config__free_unpacked
                     (WifiCsiConfig *message,
                      ProtobufCAllocator *allocator);
/* WifiCsiInfo methods */
void   wifi_csi_info__init
                     (WifiCsiInfo         *message);
size_t wifi_csi_info__get_packed_size
                     (const WifiCsiInfo   *message);
size_t wifi_csi_info__pack
                     (const WifiCsiInfo   *message,
                      uint8_t             *out);
size_t wifi_csi_info__pack_to_buffer
                     (const WifiCsiInfo   *message,
                      ProtobufCBuffer     *buffer);
WifiCsiInfo *
       wifi_csi_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_csi_info__free_unpacked
                     (WifiCsiInfo *message,
                      ProtobufCAllocator *allocator);
/* WifiAntGpio methods */
void   wifi_ant_gpio__init
                     (WifiAntGpio         *message);
size_t wifi_ant_gpio__get_packed_size
                     (const WifiAntGpio   *message);
size_t wifi_ant_gpio__pack
                     (const WifiAntGpio   *message,
                      uint8_t             *out);
size_t wifi_ant_gpio__pack_to_buffer
                     (const WifiAntGpio   *message,
                      ProtobufCBuffer     *buffer);
WifiAntGpio *
       wifi_ant_gpio__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_ant_gpio__free_unpacked
                     (WifiAntGpio *message,
                      ProtobufCAllocator *allocator);
/* WifiAntGpioConfig methods */
void   wifi_ant_gpio_config__init
                     (WifiAntGpioConfig         *message);
size_t wifi_ant_gpio_config__get_packed_size
                     (const WifiAntGpioConfig   *message);
size_t wifi_ant_gpio_config__pack
                     (const WifiAntGpioConfig   *message,
                      uint8_t             *out);
size_t wifi_ant_gpio_config__pack_to_buffer
                     (const WifiAntGpioConfig   *message,
                      ProtobufCBuffer     *buffer);
WifiAntGpioConfig *
       wifi_ant_gpio_config__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_ant_gpio_config__free_unpacked
                     (WifiAntGpioConfig *message,
                      ProtobufCAllocator *allocator);
/* WifiAntConfig methods */
void   wifi_ant_config__init
                     (WifiAntConfig         *message);
size_t wifi_ant_config__get_packed_size
                     (const WifiAntConfig   *message);
size_t wifi_ant_config__pack
                     (const WifiAntConfig   *message,
                      uint8_t             *out);
size_t wifi_ant_config__pack_to_buffer
                     (const WifiAntConfig   *message,
                      ProtobufCBuffer     *buffer);
WifiAntConfig *
       wifi_ant_config__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_ant_config__free_unpacked
                     (WifiAntConfig *message,
                      ProtobufCAllocator *allocator);
/* WifiActionTxReq methods */
void   wifi_action_tx_req__init
                     (WifiActionTxReq         *message);
size_t wifi_action_tx_req__get_packed_size
                     (const WifiActionTxReq   *message);
size_t wifi_action_tx_req__pack
                     (const WifiActionTxReq   *message,
                      uint8_t             *out);
size_t wifi_action_tx_req__pack_to_buffer
                     (const WifiActionTxReq   *message,
                      ProtobufCBuffer     *buffer);
WifiActionTxReq *
       wifi_action_tx_req__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_action_tx_req__free_unpacked
                     (WifiActionTxReq *message,
                      ProtobufCAllocator *allocator);
/* WifiFtmInitiatorCfg methods */
void   wifi_ftm_initiator_cfg__init
                     (WifiFtmInitiatorCfg         *message);
size_t wifi_ftm_initiator_cfg__get_packed_size
                     (const WifiFtmInitiatorCfg   *message);
size_t wifi_ftm_initiator_cfg__pack
                     (const WifiFtmInitiatorCfg   *message,
                      uint8_t             *out);
size_t wifi_ftm_initiator_cfg__pack_to_buffer
                     (const WifiFtmInitiatorCfg   *message,
                      ProtobufCBuffer     *buffer);
WifiFtmInitiatorCfg *
       wifi_ftm_initiator_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_ftm_initiator_cfg__free_unpacked
                     (WifiFtmInitiatorCfg *message,
                      ProtobufCAllocator *allocator);
/* WifiEventStaScanDone methods */
void   wifi_event_sta_scan_done__init
                     (WifiEventStaScanDone         *message);
size_t wifi_event_sta_scan_done__get_packed_size
                     (const WifiEventStaScanDone   *message);
size_t wifi_event_sta_scan_done__pack
                     (const WifiEventStaScanDone   *message,
                      uint8_t             *out);
size_t wifi_event_sta_scan_done__pack_to_buffer
                     (const WifiEventStaScanDone   *message,
                      ProtobufCBuffer     *buffer);
WifiEventStaScanDone *
       wifi_event_sta_scan_done__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_event_sta_scan_done__free_unpacked
                     (WifiEventStaScanDone *message,
                      ProtobufCAllocator *allocator);
/* WifiEventStaConnected methods */
void   wifi_event_sta_connected__init
                     (WifiEventStaConnected         *message);
size_t wifi_event_sta_connected__get_packed_size
                     (const WifiEventStaConnected   *message);
size_t wifi_event_sta_connected__pack
                     (const WifiEventStaConnected   *message,
                      uint8_t             *out);
size_t wifi_event_sta_connected__pack_to_buffer
                     (const WifiEventStaConnected   *message,
                      ProtobufCBuffer     *buffer);
WifiEventStaConnected *
       wifi_event_sta_connected__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_event_sta_connected__free_unpacked
                     (WifiEventStaConnected *message,
                      ProtobufCAllocator *allocator);
/* WifiEventStaDisconnected methods */
void   wifi_event_sta_disconnected__init
                     (WifiEventStaDisconnected         *message);
size_t wifi_event_sta_disconnected__get_packed_size
                     (const WifiEventStaDisconnected   *message);
size_t wifi_event_sta_disconnected__pack
                     (const WifiEventStaDisconnected   *message,
                      uint8_t             *out);
size_t wifi_event_sta_disconnected__pack_to_buffer
                     (const WifiEventStaDisconnected   *message,
                      ProtobufCBuffer     *buffer);
WifiEventStaDisconnected *
       wifi_event_sta_disconnected__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_event_sta_disconnected__free_unpacked
                     (WifiEventStaDisconnected *message,
                      ProtobufCAllocator *allocator);
/* WifiEventStaAuthmodeChange methods */
void   wifi_event_sta_authmode_change__init
                     (WifiEventStaAuthmodeChange         *message);
size_t wifi_event_sta_authmode_change__get_packed_size
                     (const WifiEventStaAuthmodeChange   *message);
size_t wifi_event_sta_authmode_change__pack
                     (const WifiEventStaAuthmodeChange   *message,
                      uint8_t             *out);
size_t wifi_event_sta_authmode_change__pack_to_buffer
                     (const WifiEventStaAuthmodeChange   *message,
                      ProtobufCBuffer     *buffer);
WifiEventStaAuthmodeChange *
       wifi_event_sta_authmode_change__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_event_sta_authmode_change__free_unpacked
                     (WifiEventStaAuthmodeChange *message,
                      ProtobufCAllocator *allocator);
/* WifiEventStaWpsErPin methods */
void   wifi_event_sta_wps_er_pin__init
                     (WifiEventStaWpsErPin         *message);
size_t wifi_event_sta_wps_er_pin__get_packed_size
                     (const WifiEventStaWpsErPin   *message);
size_t wifi_event_sta_wps_er_pin__pack
                     (const WifiEventStaWpsErPin   *message,
                      uint8_t             *out);
size_t wifi_event_sta_wps_er_pin__pack_to_buffer
                     (const WifiEventStaWpsErPin   *message,
                      ProtobufCBuffer     *buffer);
WifiEventStaWpsErPin *
       wifi_event_sta_wps_er_pin__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_event_sta_wps_er_pin__free_unpacked
                     (WifiEventStaWpsErPin *message,
                      ProtobufCAllocator *allocator);
/* ApCred methods */
void   ap_cred__init
                     (ApCred         *message);
size_t ap_cred__get_packed_size
                     (const ApCred   *message);
size_t ap_cred__pack
                     (const ApCred   *message,
                      uint8_t             *out);
size_t ap_cred__pack_to_buffer
                     (const ApCred   *message,
                      ProtobufCBuffer     *buffer);
ApCred *
       ap_cred__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   ap_cred__free_unpacked
                     (ApCred *message,
                      ProtobufCAllocator *allocator);
/* WifiEventStaWpsErSuccess methods */
void   wifi_event_sta_wps_er_success__init
                     (WifiEventStaWpsErSuccess         *message);
size_t wifi_event_sta_wps_er_success__get_packed_size
                     (const WifiEventStaWpsErSuccess   *message);
size_t wifi_event_sta_wps_er_success__pack
                     (const WifiEventStaWpsErSuccess   *message,
                      uint8_t             *out);
size_t wifi_event_sta_wps_er_success__pack_to_buffer
                     (const WifiEventStaWpsErSuccess   *message,
                      ProtobufCBuffer     *buffer);
WifiEventStaWpsErSuccess *
       wifi_event_sta_wps_er_success__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_event_sta_wps_er_success__free_unpacked
                     (WifiEventStaWpsErSuccess *message,
                      ProtobufCAllocator *allocator);
/* WifiEventApProbeReqRx methods */
void   wifi_event_ap_probe_req_rx__init
                     (WifiEventApProbeReqRx         *message);
size_t wifi_event_ap_probe_req_rx__get_packed_size
                     (const WifiEventApProbeReqRx   *message);
size_t wifi_event_ap_probe_req_rx__pack
                     (const WifiEventApProbeReqRx   *message,
                      uint8_t             *out);
size_t wifi_event_ap_probe_req_rx__pack_to_buffer
                     (const WifiEventApProbeReqRx   *message,
                      ProtobufCBuffer     *buffer);
WifiEventApProbeReqRx *
       wifi_event_ap_probe_req_rx__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_event_ap_probe_req_rx__free_unpacked
                     (WifiEventApProbeReqRx *message,
                      ProtobufCAllocator *allocator);
/* WifiEventBssRssiLow methods */
void   wifi_event_bss_rssi_low__init
                     (WifiEventBssRssiLow         *message);
size_t wifi_event_bss_rssi_low__get_packed_size
                     (const WifiEventBssRssiLow   *message);
size_t wifi_event_bss_rssi_low__pack
                     (const WifiEventBssRssiLow   *message,
                      uint8_t             *out);
size_t wifi_event_bss_rssi_low__pack_to_buffer
                     (const WifiEventBssRssiLow   *message,
                      ProtobufCBuffer     *buffer);
WifiEventBssRssiLow *
       wifi_event_bss_rssi_low__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_event_bss_rssi_low__free_unpacked
                     (WifiEventBssRssiLow *message,
                      ProtobufCAllocator *allocator);
/* WifiFtmReportEntry methods */
void   wifi_ftm_report_entry__init
                     (WifiFtmReportEntry         *message);
size_t wifi_ftm_report_entry__get_packed_size
                     (const WifiFtmReportEntry   *message);
size_t wifi_ftm_report_entry__pack
                     (const WifiFtmReportEntry   *message,
                      uint8_t             *out);
size_t wifi_ftm_report_entry__pack_to_buffer
                     (const WifiFtmReportEntry   *message,
                      ProtobufCBuffer     *buffer);
WifiFtmReportEntry *
       wifi_ftm_report_entry__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_ftm_report_entry__free_unpacked
                     (WifiFtmReportEntry *message,
                      ProtobufCAllocator *allocator);
/* WifiEventFtmReport methods */
void   wifi_event_ftm_report__init
                     (WifiEventFtmReport         *message);
size_t wifi_event_ftm_report__get_packed_size
                     (const WifiEventFtmReport   *message);
size_t wifi_event_ftm_report__pack
                     (const WifiEventFtmReport   *message,
                      uint8_t             *out);
size_t wifi_event_ftm_report__pack_to_buffer
                     (const WifiEventFtmReport   *message,
                      ProtobufCBuffer     *buffer);
WifiEventFtmReport *
       wifi_event_ftm_report__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_event_ftm_report__free_unpacked
                     (WifiEventFtmReport *message,
                      ProtobufCAllocator *allocator);
/* WifiEventActionTxStatus methods */
void   wifi_event_action_tx_status__init
                     (WifiEventActionTxStatus         *message);
size_t wifi_event_action_tx_status__get_packed_size
                     (const WifiEventActionTxStatus   *message);
size_t wifi_event_action_tx_status__pack
                     (const WifiEventActionTxStatus   *message,
                      uint8_t             *out);
size_t wifi_event_action_tx_status__pack_to_buffer
                     (const WifiEventActionTxStatus   *message,
                      ProtobufCBuffer     *buffer);
WifiEventActionTxStatus *
       wifi_event_action_tx_status__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_event_action_tx_status__free_unpacked
                     (WifiEventActionTxStatus *message,
                      ProtobufCAllocator *allocator);
/* WifiEventRocDone methods */
void   wifi_event_roc_done__init
                     (WifiEventRocDone         *message);
size_t wifi_event_roc_done__get_packed_size
                     (const WifiEventRocDone   *message);
size_t wifi_event_roc_done__pack
                     (const WifiEventRocDone   *message,
                      uint8_t             *out);
size_t wifi_event_roc_done__pack_to_buffer
                     (const WifiEventRocDone   *message,
                      ProtobufCBuffer     *buffer);
WifiEventRocDone *
       wifi_event_roc_done__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_event_roc_done__free_unpacked
                     (WifiEventRocDone *message,
                      ProtobufCAllocator *allocator);
/* WifiEventApWpsRgPin methods */
void   wifi_event_ap_wps_rg_pin__init
                     (WifiEventApWpsRgPin         *message);
size_t wifi_event_ap_wps_rg_pin__get_packed_size
                     (const WifiEventApWpsRgPin   *message);
size_t wifi_event_ap_wps_rg_pin__pack
                     (const WifiEventApWpsRgPin   *message,
                      uint8_t             *out);
size_t wifi_event_ap_wps_rg_pin__pack_to_buffer
                     (const WifiEventApWpsRgPin   *message,
                      ProtobufCBuffer     *buffer);
WifiEventApWpsRgPin *
       wifi_event_ap_wps_rg_pin__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_event_ap_wps_rg_pin__free_unpacked
                     (WifiEventApWpsRgPin *message,
                      ProtobufCAllocator *allocator);
/* WifiEventApWpsRgFailReason methods */
void   wifi_event_ap_wps_rg_fail_reason__init
                     (WifiEventApWpsRgFailReason         *message);
size_t wifi_event_ap_wps_rg_fail_reason__get_packed_size
                     (const WifiEventApWpsRgFailReason   *message);
size_t wifi_event_ap_wps_rg_fail_reason__pack
                     (const WifiEventApWpsRgFailReason   *message,
                      uint8_t             *out);
size_t wifi_event_ap_wps_rg_fail_reason__pack_to_buffer
                     (const WifiEventApWpsRgFailReason   *message,
                      ProtobufCBuffer     *buffer);
WifiEventApWpsRgFailReason *
       wifi_event_ap_wps_rg_fail_reason__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_event_ap_wps_rg_fail_reason__free_unpacked
                     (WifiEventApWpsRgFailReason *message,
                      ProtobufCAllocator *allocator);
/* WifiEventApWpsRgSuccess methods */
void   wifi_event_ap_wps_rg_success__init
                     (WifiEventApWpsRgSuccess         *message);
size_t wifi_event_ap_wps_rg_success__get_packed_size
                     (const WifiEventApWpsRgSuccess   *message);
size_t wifi_event_ap_wps_rg_success__pack
                     (const WifiEventApWpsRgSuccess   *message,
                      uint8_t             *out);
size_t wifi_event_ap_wps_rg_success__pack_to_buffer
                     (const WifiEventApWpsRgSuccess   *message,
                      ProtobufCBuffer     *buffer);
WifiEventApWpsRgSuccess *
       wifi_event_ap_wps_rg_success__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_event_ap_wps_rg_success__free_unpacked
                     (WifiEventApWpsRgSuccess *message,
                      ProtobufCAllocator *allocator);
/* WifiProtocols methods */
void   wifi_protocols__init
                     (WifiProtocols         *message);
size_t wifi_protocols__get_packed_size
                     (const WifiProtocols   *message);
size_t wifi_protocols__pack
                     (const WifiProtocols   *message,
                      uint8_t             *out);
size_t wifi_protocols__pack_to_buffer
                     (const WifiProtocols   *message,
                      ProtobufCBuffer     *buffer);
WifiProtocols *
       wifi_protocols__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_protocols__free_unpacked
                     (WifiProtocols *message,
                      ProtobufCAllocator *allocator);
/* WifiBandwidths methods */
void   wifi_bandwidths__init
                     (WifiBandwidths         *message);
size_t wifi_bandwidths__get_packed_size
                     (const WifiBandwidths   *message);
size_t wifi_bandwidths__pack
                     (const WifiBandwidths   *message,
                      uint8_t             *out);
size_t wifi_bandwidths__pack_to_buffer
                     (const WifiBandwidths   *message,
                      ProtobufCBuffer     *buffer);
WifiBandwidths *
       wifi_bandwidths__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   wifi_bandwidths__free_unpacked
                     (WifiBandwidths *message,
                      ProtobufCAllocator *allocator);
/* ConnectedSTAList methods */
void   connected_stalist__init
                     (ConnectedSTAList         *message);
size_t connected_stalist__get_packed_size
                     (const ConnectedSTAList   *message);
size_t connected_stalist__pack
                     (const ConnectedSTAList   *message,
                      uint8_t             *out);
size_t connected_stalist__pack_to_buffer
                     (const ConnectedSTAList   *message,
                      ProtobufCBuffer     *buffer);
ConnectedSTAList *
       connected_stalist__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   connected_stalist__free_unpacked
                     (ConnectedSTAList *message,
                      ProtobufCAllocator *allocator);
/* RpcReqGetMacAddress methods */
void   rpc__req__get_mac_address__init
                     (RpcReqGetMacAddress         *message);
size_t rpc__req__get_mac_address__get_packed_size
                     (const RpcReqGetMacAddress   *message);
size_t rpc__req__get_mac_address__pack
                     (const RpcReqGetMacAddress   *message,
                      uint8_t             *out);
size_t rpc__req__get_mac_address__pack_to_buffer
                     (const RpcReqGetMacAddress   *message,
                      ProtobufCBuffer     *buffer);
RpcReqGetMacAddress *
       rpc__req__get_mac_address__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__get_mac_address__free_unpacked
                     (RpcReqGetMacAddress *message,
                      ProtobufCAllocator *allocator);
/* RpcRespGetMacAddress methods */
void   rpc__resp__get_mac_address__init
                     (RpcRespGetMacAddress         *message);
size_t rpc__resp__get_mac_address__get_packed_size
                     (const RpcRespGetMacAddress   *message);
size_t rpc__resp__get_mac_address__pack
                     (const RpcRespGetMacAddress   *message,
                      uint8_t             *out);
size_t rpc__resp__get_mac_address__pack_to_buffer
                     (const RpcRespGetMacAddress   *message,
                      ProtobufCBuffer     *buffer);
RpcRespGetMacAddress *
       rpc__resp__get_mac_address__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__get_mac_address__free_unpacked
                     (RpcRespGetMacAddress *message,
                      ProtobufCAllocator *allocator);
/* RpcReqGetMode methods */
void   rpc__req__get_mode__init
                     (RpcReqGetMode         *message);
size_t rpc__req__get_mode__get_packed_size
                     (const RpcReqGetMode   *message);
size_t rpc__req__get_mode__pack
                     (const RpcReqGetMode   *message,
                      uint8_t             *out);
size_t rpc__req__get_mode__pack_to_buffer
                     (const RpcReqGetMode   *message,
                      ProtobufCBuffer     *buffer);
RpcReqGetMode *
       rpc__req__get_mode__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__get_mode__free_unpacked
                     (RpcReqGetMode *message,
                      ProtobufCAllocator *allocator);
/* RpcRespGetMode methods */
void   rpc__resp__get_mode__init
                     (RpcRespGetMode         *message);
size_t rpc__resp__get_mode__get_packed_size
                     (const RpcRespGetMode   *message);
size_t rpc__resp__get_mode__pack
                     (const RpcRespGetMode   *message,
                      uint8_t             *out);
size_t rpc__resp__get_mode__pack_to_buffer
                     (const RpcRespGetMode   *message,
                      ProtobufCBuffer     *buffer);
RpcRespGetMode *
       rpc__resp__get_mode__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__get_mode__free_unpacked
                     (RpcRespGetMode *message,
                      ProtobufCAllocator *allocator);
/* RpcReqSetMode methods */
void   rpc__req__set_mode__init
                     (RpcReqSetMode         *message);
size_t rpc__req__set_mode__get_packed_size
                     (const RpcReqSetMode   *message);
size_t rpc__req__set_mode__pack
                     (const RpcReqSetMode   *message,
                      uint8_t             *out);
size_t rpc__req__set_mode__pack_to_buffer
                     (const RpcReqSetMode   *message,
                      ProtobufCBuffer     *buffer);
RpcReqSetMode *
       rpc__req__set_mode__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__set_mode__free_unpacked
                     (RpcReqSetMode *message,
                      ProtobufCAllocator *allocator);
/* RpcRespSetMode methods */
void   rpc__resp__set_mode__init
                     (RpcRespSetMode         *message);
size_t rpc__resp__set_mode__get_packed_size
                     (const RpcRespSetMode   *message);
size_t rpc__resp__set_mode__pack
                     (const RpcRespSetMode   *message,
                      uint8_t             *out);
size_t rpc__resp__set_mode__pack_to_buffer
                     (const RpcRespSetMode   *message,
                      ProtobufCBuffer     *buffer);
RpcRespSetMode *
       rpc__resp__set_mode__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__set_mode__free_unpacked
                     (RpcRespSetMode *message,
                      ProtobufCAllocator *allocator);
/* RpcReqGetPs methods */
void   rpc__req__get_ps__init
                     (RpcReqGetPs         *message);
size_t rpc__req__get_ps__get_packed_size
                     (const RpcReqGetPs   *message);
size_t rpc__req__get_ps__pack
                     (const RpcReqGetPs   *message,
                      uint8_t             *out);
size_t rpc__req__get_ps__pack_to_buffer
                     (const RpcReqGetPs   *message,
                      ProtobufCBuffer     *buffer);
RpcReqGetPs *
       rpc__req__get_ps__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__get_ps__free_unpacked
                     (RpcReqGetPs *message,
                      ProtobufCAllocator *allocator);
/* RpcRespGetPs methods */
void   rpc__resp__get_ps__init
                     (RpcRespGetPs         *message);
size_t rpc__resp__get_ps__get_packed_size
                     (const RpcRespGetPs   *message);
size_t rpc__resp__get_ps__pack
                     (const RpcRespGetPs   *message,
                      uint8_t             *out);
size_t rpc__resp__get_ps__pack_to_buffer
                     (const RpcRespGetPs   *message,
                      ProtobufCBuffer     *buffer);
RpcRespGetPs *
       rpc__resp__get_ps__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__get_ps__free_unpacked
                     (RpcRespGetPs *message,
                      ProtobufCAllocator *allocator);
/* RpcReqSetPs methods */
void   rpc__req__set_ps__init
                     (RpcReqSetPs         *message);
size_t rpc__req__set_ps__get_packed_size
                     (const RpcReqSetPs   *message);
size_t rpc__req__set_ps__pack
                     (const RpcReqSetPs   *message,
                      uint8_t             *out);
size_t rpc__req__set_ps__pack_to_buffer
                     (const RpcReqSetPs   *message,
                      ProtobufCBuffer     *buffer);
RpcReqSetPs *
       rpc__req__set_ps__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__set_ps__free_unpacked
                     (RpcReqSetPs *message,
                      ProtobufCAllocator *allocator);
/* RpcRespSetPs methods */
void   rpc__resp__set_ps__init
                     (RpcRespSetPs         *message);
size_t rpc__resp__set_ps__get_packed_size
                     (const RpcRespSetPs   *message);
size_t rpc__resp__set_ps__pack
                     (const RpcRespSetPs   *message,
                      uint8_t             *out);
size_t rpc__resp__set_ps__pack_to_buffer
                     (const RpcRespSetPs   *message,
                      ProtobufCBuffer     *buffer);
RpcRespSetPs *
       rpc__resp__set_ps__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__set_ps__free_unpacked
                     (RpcRespSetPs *message,
                      ProtobufCAllocator *allocator);
/* RpcReqSetMacAddress methods */
void   rpc__req__set_mac_address__init
                     (RpcReqSetMacAddress         *message);
size_t rpc__req__set_mac_address__get_packed_size
                     (const RpcReqSetMacAddress   *message);
size_t rpc__req__set_mac_address__pack
                     (const RpcReqSetMacAddress   *message,
                      uint8_t             *out);
size_t rpc__req__set_mac_address__pack_to_buffer
                     (const RpcReqSetMacAddress   *message,
                      ProtobufCBuffer     *buffer);
RpcReqSetMacAddress *
       rpc__req__set_mac_address__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__set_mac_address__free_unpacked
                     (RpcReqSetMacAddress *message,
                      ProtobufCAllocator *allocator);
/* RpcRespSetMacAddress methods */
void   rpc__resp__set_mac_address__init
                     (RpcRespSetMacAddress         *message);
size_t rpc__resp__set_mac_address__get_packed_size
                     (const RpcRespSetMacAddress   *message);
size_t rpc__resp__set_mac_address__pack
                     (const RpcRespSetMacAddress   *message,
                      uint8_t             *out);
size_t rpc__resp__set_mac_address__pack_to_buffer
                     (const RpcRespSetMacAddress   *message,
                      ProtobufCBuffer     *buffer);
RpcRespSetMacAddress *
       rpc__resp__set_mac_address__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__set_mac_address__free_unpacked
                     (RpcRespSetMacAddress *message,
                      ProtobufCAllocator *allocator);
/* RpcReqOTABegin methods */
void   rpc__req__otabegin__init
                     (RpcReqOTABegin         *message);
size_t rpc__req__otabegin__get_packed_size
                     (const RpcReqOTABegin   *message);
size_t rpc__req__otabegin__pack
                     (const RpcReqOTABegin   *message,
                      uint8_t             *out);
size_t rpc__req__otabegin__pack_to_buffer
                     (const RpcReqOTABegin   *message,
                      ProtobufCBuffer     *buffer);
RpcReqOTABegin *
       rpc__req__otabegin__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__otabegin__free_unpacked
                     (RpcReqOTABegin *message,
                      ProtobufCAllocator *allocator);
/* RpcRespOTABegin methods */
void   rpc__resp__otabegin__init
                     (RpcRespOTABegin         *message);
size_t rpc__resp__otabegin__get_packed_size
                     (const RpcRespOTABegin   *message);
size_t rpc__resp__otabegin__pack
                     (const RpcRespOTABegin   *message,
                      uint8_t             *out);
size_t rpc__resp__otabegin__pack_to_buffer
                     (const RpcRespOTABegin   *message,
                      ProtobufCBuffer     *buffer);
RpcRespOTABegin *
       rpc__resp__otabegin__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__otabegin__free_unpacked
                     (RpcRespOTABegin *message,
                      ProtobufCAllocator *allocator);
/* RpcReqOTAWrite methods */
void   rpc__req__otawrite__init
                     (RpcReqOTAWrite         *message);
size_t rpc__req__otawrite__get_packed_size
                     (const RpcReqOTAWrite   *message);
size_t rpc__req__otawrite__pack
                     (const RpcReqOTAWrite   *message,
                      uint8_t             *out);
size_t rpc__req__otawrite__pack_to_buffer
                     (const RpcReqOTAWrite   *message,
                      ProtobufCBuffer     *buffer);
RpcReqOTAWrite *
       rpc__req__otawrite__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__otawrite__free_unpacked
                     (RpcReqOTAWrite *message,
                      ProtobufCAllocator *allocator);
/* RpcRespOTAWrite methods */
void   rpc__resp__otawrite__init
                     (RpcRespOTAWrite         *message);
size_t rpc__resp__otawrite__get_packed_size
                     (const RpcRespOTAWrite   *message);
size_t rpc__resp__otawrite__pack
                     (const RpcRespOTAWrite   *message,
                      uint8_t             *out);
size_t rpc__resp__otawrite__pack_to_buffer
                     (const RpcRespOTAWrite   *message,
                      ProtobufCBuffer     *buffer);
RpcRespOTAWrite *
       rpc__resp__otawrite__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__otawrite__free_unpacked
                     (RpcRespOTAWrite *message,
                      ProtobufCAllocator *allocator);
/* RpcReqOTAEnd methods */
void   rpc__req__otaend__init
                     (RpcReqOTAEnd         *message);
size_t rpc__req__otaend__get_packed_size
                     (const RpcReqOTAEnd   *message);
size_t rpc__req__otaend__pack
                     (const RpcReqOTAEnd   *message,
                      uint8_t             *out);
size_t rpc__req__otaend__pack_to_buffer
                     (const RpcReqOTAEnd   *message,
                      ProtobufCBuffer     *buffer);
RpcReqOTAEnd *
       rpc__req__otaend__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__otaend__free_unpacked
                     (RpcReqOTAEnd *message,
                      ProtobufCAllocator *allocator);
/* RpcRespOTAEnd methods */
void   rpc__resp__otaend__init
                     (RpcRespOTAEnd         *message);
size_t rpc__resp__otaend__get_packed_size
                     (const RpcRespOTAEnd   *message);
size_t rpc__resp__otaend__pack
                     (const RpcRespOTAEnd   *message,
                      uint8_t             *out);
size_t rpc__resp__otaend__pack_to_buffer
                     (const RpcRespOTAEnd   *message,
                      ProtobufCBuffer     *buffer);
RpcRespOTAEnd *
       rpc__resp__otaend__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__otaend__free_unpacked
                     (RpcRespOTAEnd *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiSetMaxTxPower methods */
void   rpc__req__wifi_set_max_tx_power__init
                     (RpcReqWifiSetMaxTxPower         *message);
size_t rpc__req__wifi_set_max_tx_power__get_packed_size
                     (const RpcReqWifiSetMaxTxPower   *message);
size_t rpc__req__wifi_set_max_tx_power__pack
                     (const RpcReqWifiSetMaxTxPower   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_set_max_tx_power__pack_to_buffer
                     (const RpcReqWifiSetMaxTxPower   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiSetMaxTxPower *
       rpc__req__wifi_set_max_tx_power__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_set_max_tx_power__free_unpacked
                     (RpcReqWifiSetMaxTxPower *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiSetMaxTxPower methods */
void   rpc__resp__wifi_set_max_tx_power__init
                     (RpcRespWifiSetMaxTxPower         *message);
size_t rpc__resp__wifi_set_max_tx_power__get_packed_size
                     (const RpcRespWifiSetMaxTxPower   *message);
size_t rpc__resp__wifi_set_max_tx_power__pack
                     (const RpcRespWifiSetMaxTxPower   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_set_max_tx_power__pack_to_buffer
                     (const RpcRespWifiSetMaxTxPower   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiSetMaxTxPower *
       rpc__resp__wifi_set_max_tx_power__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_set_max_tx_power__free_unpacked
                     (RpcRespWifiSetMaxTxPower *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiGetMaxTxPower methods */
void   rpc__req__wifi_get_max_tx_power__init
                     (RpcReqWifiGetMaxTxPower         *message);
size_t rpc__req__wifi_get_max_tx_power__get_packed_size
                     (const RpcReqWifiGetMaxTxPower   *message);
size_t rpc__req__wifi_get_max_tx_power__pack
                     (const RpcReqWifiGetMaxTxPower   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_get_max_tx_power__pack_to_buffer
                     (const RpcReqWifiGetMaxTxPower   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiGetMaxTxPower *
       rpc__req__wifi_get_max_tx_power__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_get_max_tx_power__free_unpacked
                     (RpcReqWifiGetMaxTxPower *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiGetMaxTxPower methods */
void   rpc__resp__wifi_get_max_tx_power__init
                     (RpcRespWifiGetMaxTxPower         *message);
size_t rpc__resp__wifi_get_max_tx_power__get_packed_size
                     (const RpcRespWifiGetMaxTxPower   *message);
size_t rpc__resp__wifi_get_max_tx_power__pack
                     (const RpcRespWifiGetMaxTxPower   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_get_max_tx_power__pack_to_buffer
                     (const RpcRespWifiGetMaxTxPower   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiGetMaxTxPower *
       rpc__resp__wifi_get_max_tx_power__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_get_max_tx_power__free_unpacked
                     (RpcRespWifiGetMaxTxPower *message,
                      ProtobufCAllocator *allocator);
/* RpcReqConfigHeartbeat methods */
void   rpc__req__config_heartbeat__init
                     (RpcReqConfigHeartbeat         *message);
size_t rpc__req__config_heartbeat__get_packed_size
                     (const RpcReqConfigHeartbeat   *message);
size_t rpc__req__config_heartbeat__pack
                     (const RpcReqConfigHeartbeat   *message,
                      uint8_t             *out);
size_t rpc__req__config_heartbeat__pack_to_buffer
                     (const RpcReqConfigHeartbeat   *message,
                      ProtobufCBuffer     *buffer);
RpcReqConfigHeartbeat *
       rpc__req__config_heartbeat__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__config_heartbeat__free_unpacked
                     (RpcReqConfigHeartbeat *message,
                      ProtobufCAllocator *allocator);
/* RpcRespConfigHeartbeat methods */
void   rpc__resp__config_heartbeat__init
                     (RpcRespConfigHeartbeat         *message);
size_t rpc__resp__config_heartbeat__get_packed_size
                     (const RpcRespConfigHeartbeat   *message);
size_t rpc__resp__config_heartbeat__pack
                     (const RpcRespConfigHeartbeat   *message,
                      uint8_t             *out);
size_t rpc__resp__config_heartbeat__pack_to_buffer
                     (const RpcRespConfigHeartbeat   *message,
                      ProtobufCBuffer     *buffer);
RpcRespConfigHeartbeat *
       rpc__resp__config_heartbeat__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__config_heartbeat__free_unpacked
                     (RpcRespConfigHeartbeat *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiInit methods */
void   rpc__req__wifi_init__init
                     (RpcReqWifiInit         *message);
size_t rpc__req__wifi_init__get_packed_size
                     (const RpcReqWifiInit   *message);
size_t rpc__req__wifi_init__pack
                     (const RpcReqWifiInit   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_init__pack_to_buffer
                     (const RpcReqWifiInit   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiInit *
       rpc__req__wifi_init__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_init__free_unpacked
                     (RpcReqWifiInit *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiInit methods */
void   rpc__resp__wifi_init__init
                     (RpcRespWifiInit         *message);
size_t rpc__resp__wifi_init__get_packed_size
                     (const RpcRespWifiInit   *message);
size_t rpc__resp__wifi_init__pack
                     (const RpcRespWifiInit   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_init__pack_to_buffer
                     (const RpcRespWifiInit   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiInit *
       rpc__resp__wifi_init__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_init__free_unpacked
                     (RpcRespWifiInit *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiDeinit methods */
void   rpc__req__wifi_deinit__init
                     (RpcReqWifiDeinit         *message);
size_t rpc__req__wifi_deinit__get_packed_size
                     (const RpcReqWifiDeinit   *message);
size_t rpc__req__wifi_deinit__pack
                     (const RpcReqWifiDeinit   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_deinit__pack_to_buffer
                     (const RpcReqWifiDeinit   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiDeinit *
       rpc__req__wifi_deinit__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_deinit__free_unpacked
                     (RpcReqWifiDeinit *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiDeinit methods */
void   rpc__resp__wifi_deinit__init
                     (RpcRespWifiDeinit         *message);
size_t rpc__resp__wifi_deinit__get_packed_size
                     (const RpcRespWifiDeinit   *message);
size_t rpc__resp__wifi_deinit__pack
                     (const RpcRespWifiDeinit   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_deinit__pack_to_buffer
                     (const RpcRespWifiDeinit   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiDeinit *
       rpc__resp__wifi_deinit__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_deinit__free_unpacked
                     (RpcRespWifiDeinit *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiSetConfig methods */
void   rpc__req__wifi_set_config__init
                     (RpcReqWifiSetConfig         *message);
size_t rpc__req__wifi_set_config__get_packed_size
                     (const RpcReqWifiSetConfig   *message);
size_t rpc__req__wifi_set_config__pack
                     (const RpcReqWifiSetConfig   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_set_config__pack_to_buffer
                     (const RpcReqWifiSetConfig   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiSetConfig *
       rpc__req__wifi_set_config__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_set_config__free_unpacked
                     (RpcReqWifiSetConfig *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiSetConfig methods */
void   rpc__resp__wifi_set_config__init
                     (RpcRespWifiSetConfig         *message);
size_t rpc__resp__wifi_set_config__get_packed_size
                     (const RpcRespWifiSetConfig   *message);
size_t rpc__resp__wifi_set_config__pack
                     (const RpcRespWifiSetConfig   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_set_config__pack_to_buffer
                     (const RpcRespWifiSetConfig   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiSetConfig *
       rpc__resp__wifi_set_config__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_set_config__free_unpacked
                     (RpcRespWifiSetConfig *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiGetConfig methods */
void   rpc__req__wifi_get_config__init
                     (RpcReqWifiGetConfig         *message);
size_t rpc__req__wifi_get_config__get_packed_size
                     (const RpcReqWifiGetConfig   *message);
size_t rpc__req__wifi_get_config__pack
                     (const RpcReqWifiGetConfig   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_get_config__pack_to_buffer
                     (const RpcReqWifiGetConfig   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiGetConfig *
       rpc__req__wifi_get_config__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_get_config__free_unpacked
                     (RpcReqWifiGetConfig *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiGetConfig methods */
void   rpc__resp__wifi_get_config__init
                     (RpcRespWifiGetConfig         *message);
size_t rpc__resp__wifi_get_config__get_packed_size
                     (const RpcRespWifiGetConfig   *message);
size_t rpc__resp__wifi_get_config__pack
                     (const RpcRespWifiGetConfig   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_get_config__pack_to_buffer
                     (const RpcRespWifiGetConfig   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiGetConfig *
       rpc__resp__wifi_get_config__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_get_config__free_unpacked
                     (RpcRespWifiGetConfig *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiConnect methods */
void   rpc__req__wifi_connect__init
                     (RpcReqWifiConnect         *message);
size_t rpc__req__wifi_connect__get_packed_size
                     (const RpcReqWifiConnect   *message);
size_t rpc__req__wifi_connect__pack
                     (const RpcReqWifiConnect   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_connect__pack_to_buffer
                     (const RpcReqWifiConnect   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiConnect *
       rpc__req__wifi_connect__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_connect__free_unpacked
                     (RpcReqWifiConnect *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiConnect methods */
void   rpc__resp__wifi_connect__init
                     (RpcRespWifiConnect         *message);
size_t rpc__resp__wifi_connect__get_packed_size
                     (const RpcRespWifiConnect   *message);
size_t rpc__resp__wifi_connect__pack
                     (const RpcRespWifiConnect   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_connect__pack_to_buffer
                     (const RpcRespWifiConnect   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiConnect *
       rpc__resp__wifi_connect__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_connect__free_unpacked
                     (RpcRespWifiConnect *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiDisconnect methods */
void   rpc__req__wifi_disconnect__init
                     (RpcReqWifiDisconnect         *message);
size_t rpc__req__wifi_disconnect__get_packed_size
                     (const RpcReqWifiDisconnect   *message);
size_t rpc__req__wifi_disconnect__pack
                     (const RpcReqWifiDisconnect   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_disconnect__pack_to_buffer
                     (const RpcReqWifiDisconnect   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiDisconnect *
       rpc__req__wifi_disconnect__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_disconnect__free_unpacked
                     (RpcReqWifiDisconnect *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiDisconnect methods */
void   rpc__resp__wifi_disconnect__init
                     (RpcRespWifiDisconnect         *message);
size_t rpc__resp__wifi_disconnect__get_packed_size
                     (const RpcRespWifiDisconnect   *message);
size_t rpc__resp__wifi_disconnect__pack
                     (const RpcRespWifiDisconnect   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_disconnect__pack_to_buffer
                     (const RpcRespWifiDisconnect   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiDisconnect *
       rpc__resp__wifi_disconnect__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_disconnect__free_unpacked
                     (RpcRespWifiDisconnect *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiStart methods */
void   rpc__req__wifi_start__init
                     (RpcReqWifiStart         *message);
size_t rpc__req__wifi_start__get_packed_size
                     (const RpcReqWifiStart   *message);
size_t rpc__req__wifi_start__pack
                     (const RpcReqWifiStart   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_start__pack_to_buffer
                     (const RpcReqWifiStart   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiStart *
       rpc__req__wifi_start__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_start__free_unpacked
                     (RpcReqWifiStart *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiStart methods */
void   rpc__resp__wifi_start__init
                     (RpcRespWifiStart         *message);
size_t rpc__resp__wifi_start__get_packed_size
                     (const RpcRespWifiStart   *message);
size_t rpc__resp__wifi_start__pack
                     (const RpcRespWifiStart   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_start__pack_to_buffer
                     (const RpcRespWifiStart   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiStart *
       rpc__resp__wifi_start__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_start__free_unpacked
                     (RpcRespWifiStart *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiStop methods */
void   rpc__req__wifi_stop__init
                     (RpcReqWifiStop         *message);
size_t rpc__req__wifi_stop__get_packed_size
                     (const RpcReqWifiStop   *message);
size_t rpc__req__wifi_stop__pack
                     (const RpcReqWifiStop   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_stop__pack_to_buffer
                     (const RpcReqWifiStop   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiStop *
       rpc__req__wifi_stop__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_stop__free_unpacked
                     (RpcReqWifiStop *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiStop methods */
void   rpc__resp__wifi_stop__init
                     (RpcRespWifiStop         *message);
size_t rpc__resp__wifi_stop__get_packed_size
                     (const RpcRespWifiStop   *message);
size_t rpc__resp__wifi_stop__pack
                     (const RpcRespWifiStop   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_stop__pack_to_buffer
                     (const RpcRespWifiStop   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiStop *
       rpc__resp__wifi_stop__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_stop__free_unpacked
                     (RpcRespWifiStop *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiScanStart methods */
void   rpc__req__wifi_scan_start__init
                     (RpcReqWifiScanStart         *message);
size_t rpc__req__wifi_scan_start__get_packed_size
                     (const RpcReqWifiScanStart   *message);
size_t rpc__req__wifi_scan_start__pack
                     (const RpcReqWifiScanStart   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_scan_start__pack_to_buffer
                     (const RpcReqWifiScanStart   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiScanStart *
       rpc__req__wifi_scan_start__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_scan_start__free_unpacked
                     (RpcReqWifiScanStart *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiScanStart methods */
void   rpc__resp__wifi_scan_start__init
                     (RpcRespWifiScanStart         *message);
size_t rpc__resp__wifi_scan_start__get_packed_size
                     (const RpcRespWifiScanStart   *message);
size_t rpc__resp__wifi_scan_start__pack
                     (const RpcRespWifiScanStart   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_scan_start__pack_to_buffer
                     (const RpcRespWifiScanStart   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiScanStart *
       rpc__resp__wifi_scan_start__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_scan_start__free_unpacked
                     (RpcRespWifiScanStart *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiScanStop methods */
void   rpc__req__wifi_scan_stop__init
                     (RpcReqWifiScanStop         *message);
size_t rpc__req__wifi_scan_stop__get_packed_size
                     (const RpcReqWifiScanStop   *message);
size_t rpc__req__wifi_scan_stop__pack
                     (const RpcReqWifiScanStop   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_scan_stop__pack_to_buffer
                     (const RpcReqWifiScanStop   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiScanStop *
       rpc__req__wifi_scan_stop__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_scan_stop__free_unpacked
                     (RpcReqWifiScanStop *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiScanStop methods */
void   rpc__resp__wifi_scan_stop__init
                     (RpcRespWifiScanStop         *message);
size_t rpc__resp__wifi_scan_stop__get_packed_size
                     (const RpcRespWifiScanStop   *message);
size_t rpc__resp__wifi_scan_stop__pack
                     (const RpcRespWifiScanStop   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_scan_stop__pack_to_buffer
                     (const RpcRespWifiScanStop   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiScanStop *
       rpc__resp__wifi_scan_stop__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_scan_stop__free_unpacked
                     (RpcRespWifiScanStop *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiScanGetApNum methods */
void   rpc__req__wifi_scan_get_ap_num__init
                     (RpcReqWifiScanGetApNum         *message);
size_t rpc__req__wifi_scan_get_ap_num__get_packed_size
                     (const RpcReqWifiScanGetApNum   *message);
size_t rpc__req__wifi_scan_get_ap_num__pack
                     (const RpcReqWifiScanGetApNum   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_scan_get_ap_num__pack_to_buffer
                     (const RpcReqWifiScanGetApNum   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiScanGetApNum *
       rpc__req__wifi_scan_get_ap_num__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_scan_get_ap_num__free_unpacked
                     (RpcReqWifiScanGetApNum *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiScanGetApNum methods */
void   rpc__resp__wifi_scan_get_ap_num__init
                     (RpcRespWifiScanGetApNum         *message);
size_t rpc__resp__wifi_scan_get_ap_num__get_packed_size
                     (const RpcRespWifiScanGetApNum   *message);
size_t rpc__resp__wifi_scan_get_ap_num__pack
                     (const RpcRespWifiScanGetApNum   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_scan_get_ap_num__pack_to_buffer
                     (const RpcRespWifiScanGetApNum   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiScanGetApNum *
       rpc__resp__wifi_scan_get_ap_num__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_scan_get_ap_num__free_unpacked
                     (RpcRespWifiScanGetApNum *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiScanGetApRecords methods */
void   rpc__req__wifi_scan_get_ap_records__init
                     (RpcReqWifiScanGetApRecords         *message);
size_t rpc__req__wifi_scan_get_ap_records__get_packed_size
                     (const RpcReqWifiScanGetApRecords   *message);
size_t rpc__req__wifi_scan_get_ap_records__pack
                     (const RpcReqWifiScanGetApRecords   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_scan_get_ap_records__pack_to_buffer
                     (const RpcReqWifiScanGetApRecords   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiScanGetApRecords *
       rpc__req__wifi_scan_get_ap_records__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_scan_get_ap_records__free_unpacked
                     (RpcReqWifiScanGetApRecords *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiScanGetApRecords methods */
void   rpc__resp__wifi_scan_get_ap_records__init
                     (RpcRespWifiScanGetApRecords         *message);
size_t rpc__resp__wifi_scan_get_ap_records__get_packed_size
                     (const RpcRespWifiScanGetApRecords   *message);
size_t rpc__resp__wifi_scan_get_ap_records__pack
                     (const RpcRespWifiScanGetApRecords   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_scan_get_ap_records__pack_to_buffer
                     (const RpcRespWifiScanGetApRecords   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiScanGetApRecords *
       rpc__resp__wifi_scan_get_ap_records__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_scan_get_ap_records__free_unpacked
                     (RpcRespWifiScanGetApRecords *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiClearApList methods */
void   rpc__req__wifi_clear_ap_list__init
                     (RpcReqWifiClearApList         *message);
size_t rpc__req__wifi_clear_ap_list__get_packed_size
                     (const RpcReqWifiClearApList   *message);
size_t rpc__req__wifi_clear_ap_list__pack
                     (const RpcReqWifiClearApList   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_clear_ap_list__pack_to_buffer
                     (const RpcReqWifiClearApList   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiClearApList *
       rpc__req__wifi_clear_ap_list__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_clear_ap_list__free_unpacked
                     (RpcReqWifiClearApList *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiClearApList methods */
void   rpc__resp__wifi_clear_ap_list__init
                     (RpcRespWifiClearApList         *message);
size_t rpc__resp__wifi_clear_ap_list__get_packed_size
                     (const RpcRespWifiClearApList   *message);
size_t rpc__resp__wifi_clear_ap_list__pack
                     (const RpcRespWifiClearApList   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_clear_ap_list__pack_to_buffer
                     (const RpcRespWifiClearApList   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiClearApList *
       rpc__resp__wifi_clear_ap_list__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_clear_ap_list__free_unpacked
                     (RpcRespWifiClearApList *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiRestore methods */
void   rpc__req__wifi_restore__init
                     (RpcReqWifiRestore         *message);
size_t rpc__req__wifi_restore__get_packed_size
                     (const RpcReqWifiRestore   *message);
size_t rpc__req__wifi_restore__pack
                     (const RpcReqWifiRestore   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_restore__pack_to_buffer
                     (const RpcReqWifiRestore   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiRestore *
       rpc__req__wifi_restore__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_restore__free_unpacked
                     (RpcReqWifiRestore *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiRestore methods */
void   rpc__resp__wifi_restore__init
                     (RpcRespWifiRestore         *message);
size_t rpc__resp__wifi_restore__get_packed_size
                     (const RpcRespWifiRestore   *message);
size_t rpc__resp__wifi_restore__pack
                     (const RpcRespWifiRestore   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_restore__pack_to_buffer
                     (const RpcRespWifiRestore   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiRestore *
       rpc__resp__wifi_restore__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_restore__free_unpacked
                     (RpcRespWifiRestore *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiClearFastConnect methods */
void   rpc__req__wifi_clear_fast_connect__init
                     (RpcReqWifiClearFastConnect         *message);
size_t rpc__req__wifi_clear_fast_connect__get_packed_size
                     (const RpcReqWifiClearFastConnect   *message);
size_t rpc__req__wifi_clear_fast_connect__pack
                     (const RpcReqWifiClearFastConnect   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_clear_fast_connect__pack_to_buffer
                     (const RpcReqWifiClearFastConnect   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiClearFastConnect *
       rpc__req__wifi_clear_fast_connect__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_clear_fast_connect__free_unpacked
                     (RpcReqWifiClearFastConnect *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiClearFastConnect methods */
void   rpc__resp__wifi_clear_fast_connect__init
                     (RpcRespWifiClearFastConnect         *message);
size_t rpc__resp__wifi_clear_fast_connect__get_packed_size
                     (const RpcRespWifiClearFastConnect   *message);
size_t rpc__resp__wifi_clear_fast_connect__pack
                     (const RpcRespWifiClearFastConnect   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_clear_fast_connect__pack_to_buffer
                     (const RpcRespWifiClearFastConnect   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiClearFastConnect *
       rpc__resp__wifi_clear_fast_connect__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_clear_fast_connect__free_unpacked
                     (RpcRespWifiClearFastConnect *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiDeauthSta methods */
void   rpc__req__wifi_deauth_sta__init
                     (RpcReqWifiDeauthSta         *message);
size_t rpc__req__wifi_deauth_sta__get_packed_size
                     (const RpcReqWifiDeauthSta   *message);
size_t rpc__req__wifi_deauth_sta__pack
                     (const RpcReqWifiDeauthSta   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_deauth_sta__pack_to_buffer
                     (const RpcReqWifiDeauthSta   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiDeauthSta *
       rpc__req__wifi_deauth_sta__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_deauth_sta__free_unpacked
                     (RpcReqWifiDeauthSta *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiDeauthSta methods */
void   rpc__resp__wifi_deauth_sta__init
                     (RpcRespWifiDeauthSta         *message);
size_t rpc__resp__wifi_deauth_sta__get_packed_size
                     (const RpcRespWifiDeauthSta   *message);
size_t rpc__resp__wifi_deauth_sta__pack
                     (const RpcRespWifiDeauthSta   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_deauth_sta__pack_to_buffer
                     (const RpcRespWifiDeauthSta   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiDeauthSta *
       rpc__resp__wifi_deauth_sta__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_deauth_sta__free_unpacked
                     (RpcRespWifiDeauthSta *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiStaGetApInfo methods */
void   rpc__req__wifi_sta_get_ap_info__init
                     (RpcReqWifiStaGetApInfo         *message);
size_t rpc__req__wifi_sta_get_ap_info__get_packed_size
                     (const RpcReqWifiStaGetApInfo   *message);
size_t rpc__req__wifi_sta_get_ap_info__pack
                     (const RpcReqWifiStaGetApInfo   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_sta_get_ap_info__pack_to_buffer
                     (const RpcReqWifiStaGetApInfo   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiStaGetApInfo *
       rpc__req__wifi_sta_get_ap_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_sta_get_ap_info__free_unpacked
                     (RpcReqWifiStaGetApInfo *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiStaGetApInfo methods */
void   rpc__resp__wifi_sta_get_ap_info__init
                     (RpcRespWifiStaGetApInfo         *message);
size_t rpc__resp__wifi_sta_get_ap_info__get_packed_size
                     (const RpcRespWifiStaGetApInfo   *message);
size_t rpc__resp__wifi_sta_get_ap_info__pack
                     (const RpcRespWifiStaGetApInfo   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_sta_get_ap_info__pack_to_buffer
                     (const RpcRespWifiStaGetApInfo   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiStaGetApInfo *
       rpc__resp__wifi_sta_get_ap_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_sta_get_ap_info__free_unpacked
                     (RpcRespWifiStaGetApInfo *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiSetProtocol methods */
void   rpc__req__wifi_set_protocol__init
                     (RpcReqWifiSetProtocol         *message);
size_t rpc__req__wifi_set_protocol__get_packed_size
                     (const RpcReqWifiSetProtocol   *message);
size_t rpc__req__wifi_set_protocol__pack
                     (const RpcReqWifiSetProtocol   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_set_protocol__pack_to_buffer
                     (const RpcReqWifiSetProtocol   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiSetProtocol *
       rpc__req__wifi_set_protocol__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_set_protocol__free_unpacked
                     (RpcReqWifiSetProtocol *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiSetProtocol methods */
void   rpc__resp__wifi_set_protocol__init
                     (RpcRespWifiSetProtocol         *message);
size_t rpc__resp__wifi_set_protocol__get_packed_size
                     (const RpcRespWifiSetProtocol   *message);
size_t rpc__resp__wifi_set_protocol__pack
                     (const RpcRespWifiSetProtocol   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_set_protocol__pack_to_buffer
                     (const RpcRespWifiSetProtocol   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiSetProtocol *
       rpc__resp__wifi_set_protocol__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_set_protocol__free_unpacked
                     (RpcRespWifiSetProtocol *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiGetProtocol methods */
void   rpc__req__wifi_get_protocol__init
                     (RpcReqWifiGetProtocol         *message);
size_t rpc__req__wifi_get_protocol__get_packed_size
                     (const RpcReqWifiGetProtocol   *message);
size_t rpc__req__wifi_get_protocol__pack
                     (const RpcReqWifiGetProtocol   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_get_protocol__pack_to_buffer
                     (const RpcReqWifiGetProtocol   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiGetProtocol *
       rpc__req__wifi_get_protocol__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_get_protocol__free_unpacked
                     (RpcReqWifiGetProtocol *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiGetProtocol methods */
void   rpc__resp__wifi_get_protocol__init
                     (RpcRespWifiGetProtocol         *message);
size_t rpc__resp__wifi_get_protocol__get_packed_size
                     (const RpcRespWifiGetProtocol   *message);
size_t rpc__resp__wifi_get_protocol__pack
                     (const RpcRespWifiGetProtocol   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_get_protocol__pack_to_buffer
                     (const RpcRespWifiGetProtocol   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiGetProtocol *
       rpc__resp__wifi_get_protocol__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_get_protocol__free_unpacked
                     (RpcRespWifiGetProtocol *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiSetBandwidth methods */
void   rpc__req__wifi_set_bandwidth__init
                     (RpcReqWifiSetBandwidth         *message);
size_t rpc__req__wifi_set_bandwidth__get_packed_size
                     (const RpcReqWifiSetBandwidth   *message);
size_t rpc__req__wifi_set_bandwidth__pack
                     (const RpcReqWifiSetBandwidth   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_set_bandwidth__pack_to_buffer
                     (const RpcReqWifiSetBandwidth   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiSetBandwidth *
       rpc__req__wifi_set_bandwidth__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_set_bandwidth__free_unpacked
                     (RpcReqWifiSetBandwidth *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiSetBandwidth methods */
void   rpc__resp__wifi_set_bandwidth__init
                     (RpcRespWifiSetBandwidth         *message);
size_t rpc__resp__wifi_set_bandwidth__get_packed_size
                     (const RpcRespWifiSetBandwidth   *message);
size_t rpc__resp__wifi_set_bandwidth__pack
                     (const RpcRespWifiSetBandwidth   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_set_bandwidth__pack_to_buffer
                     (const RpcRespWifiSetBandwidth   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiSetBandwidth *
       rpc__resp__wifi_set_bandwidth__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_set_bandwidth__free_unpacked
                     (RpcRespWifiSetBandwidth *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiGetBandwidth methods */
void   rpc__req__wifi_get_bandwidth__init
                     (RpcReqWifiGetBandwidth         *message);
size_t rpc__req__wifi_get_bandwidth__get_packed_size
                     (const RpcReqWifiGetBandwidth   *message);
size_t rpc__req__wifi_get_bandwidth__pack
                     (const RpcReqWifiGetBandwidth   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_get_bandwidth__pack_to_buffer
                     (const RpcReqWifiGetBandwidth   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiGetBandwidth *
       rpc__req__wifi_get_bandwidth__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_get_bandwidth__free_unpacked
                     (RpcReqWifiGetBandwidth *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiGetBandwidth methods */
void   rpc__resp__wifi_get_bandwidth__init
                     (RpcRespWifiGetBandwidth         *message);
size_t rpc__resp__wifi_get_bandwidth__get_packed_size
                     (const RpcRespWifiGetBandwidth   *message);
size_t rpc__resp__wifi_get_bandwidth__pack
                     (const RpcRespWifiGetBandwidth   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_get_bandwidth__pack_to_buffer
                     (const RpcRespWifiGetBandwidth   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiGetBandwidth *
       rpc__resp__wifi_get_bandwidth__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_get_bandwidth__free_unpacked
                     (RpcRespWifiGetBandwidth *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiSetChannel methods */
void   rpc__req__wifi_set_channel__init
                     (RpcReqWifiSetChannel         *message);
size_t rpc__req__wifi_set_channel__get_packed_size
                     (const RpcReqWifiSetChannel   *message);
size_t rpc__req__wifi_set_channel__pack
                     (const RpcReqWifiSetChannel   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_set_channel__pack_to_buffer
                     (const RpcReqWifiSetChannel   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiSetChannel *
       rpc__req__wifi_set_channel__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_set_channel__free_unpacked
                     (RpcReqWifiSetChannel *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiSetChannel methods */
void   rpc__resp__wifi_set_channel__init
                     (RpcRespWifiSetChannel         *message);
size_t rpc__resp__wifi_set_channel__get_packed_size
                     (const RpcRespWifiSetChannel   *message);
size_t rpc__resp__wifi_set_channel__pack
                     (const RpcRespWifiSetChannel   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_set_channel__pack_to_buffer
                     (const RpcRespWifiSetChannel   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiSetChannel *
       rpc__resp__wifi_set_channel__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_set_channel__free_unpacked
                     (RpcRespWifiSetChannel *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiGetChannel methods */
void   rpc__req__wifi_get_channel__init
                     (RpcReqWifiGetChannel         *message);
size_t rpc__req__wifi_get_channel__get_packed_size
                     (const RpcReqWifiGetChannel   *message);
size_t rpc__req__wifi_get_channel__pack
                     (const RpcReqWifiGetChannel   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_get_channel__pack_to_buffer
                     (const RpcReqWifiGetChannel   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiGetChannel *
       rpc__req__wifi_get_channel__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_get_channel__free_unpacked
                     (RpcReqWifiGetChannel *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiGetChannel methods */
void   rpc__resp__wifi_get_channel__init
                     (RpcRespWifiGetChannel         *message);
size_t rpc__resp__wifi_get_channel__get_packed_size
                     (const RpcRespWifiGetChannel   *message);
size_t rpc__resp__wifi_get_channel__pack
                     (const RpcRespWifiGetChannel   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_get_channel__pack_to_buffer
                     (const RpcRespWifiGetChannel   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiGetChannel *
       rpc__resp__wifi_get_channel__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_get_channel__free_unpacked
                     (RpcRespWifiGetChannel *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiSetStorage methods */
void   rpc__req__wifi_set_storage__init
                     (RpcReqWifiSetStorage         *message);
size_t rpc__req__wifi_set_storage__get_packed_size
                     (const RpcReqWifiSetStorage   *message);
size_t rpc__req__wifi_set_storage__pack
                     (const RpcReqWifiSetStorage   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_set_storage__pack_to_buffer
                     (const RpcReqWifiSetStorage   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiSetStorage *
       rpc__req__wifi_set_storage__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_set_storage__free_unpacked
                     (RpcReqWifiSetStorage *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiSetStorage methods */
void   rpc__resp__wifi_set_storage__init
                     (RpcRespWifiSetStorage         *message);
size_t rpc__resp__wifi_set_storage__get_packed_size
                     (const RpcRespWifiSetStorage   *message);
size_t rpc__resp__wifi_set_storage__pack
                     (const RpcRespWifiSetStorage   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_set_storage__pack_to_buffer
                     (const RpcRespWifiSetStorage   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiSetStorage *
       rpc__resp__wifi_set_storage__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_set_storage__free_unpacked
                     (RpcRespWifiSetStorage *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiSetCountryCode methods */
void   rpc__req__wifi_set_country_code__init
                     (RpcReqWifiSetCountryCode         *message);
size_t rpc__req__wifi_set_country_code__get_packed_size
                     (const RpcReqWifiSetCountryCode   *message);
size_t rpc__req__wifi_set_country_code__pack
                     (const RpcReqWifiSetCountryCode   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_set_country_code__pack_to_buffer
                     (const RpcReqWifiSetCountryCode   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiSetCountryCode *
       rpc__req__wifi_set_country_code__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_set_country_code__free_unpacked
                     (RpcReqWifiSetCountryCode *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiSetCountryCode methods */
void   rpc__resp__wifi_set_country_code__init
                     (RpcRespWifiSetCountryCode         *message);
size_t rpc__resp__wifi_set_country_code__get_packed_size
                     (const RpcRespWifiSetCountryCode   *message);
size_t rpc__resp__wifi_set_country_code__pack
                     (const RpcRespWifiSetCountryCode   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_set_country_code__pack_to_buffer
                     (const RpcRespWifiSetCountryCode   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiSetCountryCode *
       rpc__resp__wifi_set_country_code__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_set_country_code__free_unpacked
                     (RpcRespWifiSetCountryCode *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiGetCountryCode methods */
void   rpc__req__wifi_get_country_code__init
                     (RpcReqWifiGetCountryCode         *message);
size_t rpc__req__wifi_get_country_code__get_packed_size
                     (const RpcReqWifiGetCountryCode   *message);
size_t rpc__req__wifi_get_country_code__pack
                     (const RpcReqWifiGetCountryCode   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_get_country_code__pack_to_buffer
                     (const RpcReqWifiGetCountryCode   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiGetCountryCode *
       rpc__req__wifi_get_country_code__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_get_country_code__free_unpacked
                     (RpcReqWifiGetCountryCode *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiGetCountryCode methods */
void   rpc__resp__wifi_get_country_code__init
                     (RpcRespWifiGetCountryCode         *message);
size_t rpc__resp__wifi_get_country_code__get_packed_size
                     (const RpcRespWifiGetCountryCode   *message);
size_t rpc__resp__wifi_get_country_code__pack
                     (const RpcRespWifiGetCountryCode   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_get_country_code__pack_to_buffer
                     (const RpcRespWifiGetCountryCode   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiGetCountryCode *
       rpc__resp__wifi_get_country_code__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_get_country_code__free_unpacked
                     (RpcRespWifiGetCountryCode *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiSetCountry methods */
void   rpc__req__wifi_set_country__init
                     (RpcReqWifiSetCountry         *message);
size_t rpc__req__wifi_set_country__get_packed_size
                     (const RpcReqWifiSetCountry   *message);
size_t rpc__req__wifi_set_country__pack
                     (const RpcReqWifiSetCountry   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_set_country__pack_to_buffer
                     (const RpcReqWifiSetCountry   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiSetCountry *
       rpc__req__wifi_set_country__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_set_country__free_unpacked
                     (RpcReqWifiSetCountry *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiSetCountry methods */
void   rpc__resp__wifi_set_country__init
                     (RpcRespWifiSetCountry         *message);
size_t rpc__resp__wifi_set_country__get_packed_size
                     (const RpcRespWifiSetCountry   *message);
size_t rpc__resp__wifi_set_country__pack
                     (const RpcRespWifiSetCountry   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_set_country__pack_to_buffer
                     (const RpcRespWifiSetCountry   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiSetCountry *
       rpc__resp__wifi_set_country__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_set_country__free_unpacked
                     (RpcRespWifiSetCountry *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiGetCountry methods */
void   rpc__req__wifi_get_country__init
                     (RpcReqWifiGetCountry         *message);
size_t rpc__req__wifi_get_country__get_packed_size
                     (const RpcReqWifiGetCountry   *message);
size_t rpc__req__wifi_get_country__pack
                     (const RpcReqWifiGetCountry   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_get_country__pack_to_buffer
                     (const RpcReqWifiGetCountry   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiGetCountry *
       rpc__req__wifi_get_country__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_get_country__free_unpacked
                     (RpcReqWifiGetCountry *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiGetCountry methods */
void   rpc__resp__wifi_get_country__init
                     (RpcRespWifiGetCountry         *message);
size_t rpc__resp__wifi_get_country__get_packed_size
                     (const RpcRespWifiGetCountry   *message);
size_t rpc__resp__wifi_get_country__pack
                     (const RpcRespWifiGetCountry   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_get_country__pack_to_buffer
                     (const RpcRespWifiGetCountry   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiGetCountry *
       rpc__resp__wifi_get_country__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_get_country__free_unpacked
                     (RpcRespWifiGetCountry *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiApGetStaList methods */
void   rpc__req__wifi_ap_get_sta_list__init
                     (RpcReqWifiApGetStaList         *message);
size_t rpc__req__wifi_ap_get_sta_list__get_packed_size
                     (const RpcReqWifiApGetStaList   *message);
size_t rpc__req__wifi_ap_get_sta_list__pack
                     (const RpcReqWifiApGetStaList   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_ap_get_sta_list__pack_to_buffer
                     (const RpcReqWifiApGetStaList   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiApGetStaList *
       rpc__req__wifi_ap_get_sta_list__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_ap_get_sta_list__free_unpacked
                     (RpcReqWifiApGetStaList *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiApGetStaList methods */
void   rpc__resp__wifi_ap_get_sta_list__init
                     (RpcRespWifiApGetStaList         *message);
size_t rpc__resp__wifi_ap_get_sta_list__get_packed_size
                     (const RpcRespWifiApGetStaList   *message);
size_t rpc__resp__wifi_ap_get_sta_list__pack
                     (const RpcRespWifiApGetStaList   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_ap_get_sta_list__pack_to_buffer
                     (const RpcRespWifiApGetStaList   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiApGetStaList *
       rpc__resp__wifi_ap_get_sta_list__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_ap_get_sta_list__free_unpacked
                     (RpcRespWifiApGetStaList *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiApGetStaAid methods */
void   rpc__req__wifi_ap_get_sta_aid__init
                     (RpcReqWifiApGetStaAid         *message);
size_t rpc__req__wifi_ap_get_sta_aid__get_packed_size
                     (const RpcReqWifiApGetStaAid   *message);
size_t rpc__req__wifi_ap_get_sta_aid__pack
                     (const RpcReqWifiApGetStaAid   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_ap_get_sta_aid__pack_to_buffer
                     (const RpcReqWifiApGetStaAid   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiApGetStaAid *
       rpc__req__wifi_ap_get_sta_aid__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_ap_get_sta_aid__free_unpacked
                     (RpcReqWifiApGetStaAid *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiApGetStaAid methods */
void   rpc__resp__wifi_ap_get_sta_aid__init
                     (RpcRespWifiApGetStaAid         *message);
size_t rpc__resp__wifi_ap_get_sta_aid__get_packed_size
                     (const RpcRespWifiApGetStaAid   *message);
size_t rpc__resp__wifi_ap_get_sta_aid__pack
                     (const RpcRespWifiApGetStaAid   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_ap_get_sta_aid__pack_to_buffer
                     (const RpcRespWifiApGetStaAid   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiApGetStaAid *
       rpc__resp__wifi_ap_get_sta_aid__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_ap_get_sta_aid__free_unpacked
                     (RpcRespWifiApGetStaAid *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiStaGetRssi methods */
void   rpc__req__wifi_sta_get_rssi__init
                     (RpcReqWifiStaGetRssi         *message);
size_t rpc__req__wifi_sta_get_rssi__get_packed_size
                     (const RpcReqWifiStaGetRssi   *message);
size_t rpc__req__wifi_sta_get_rssi__pack
                     (const RpcReqWifiStaGetRssi   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_sta_get_rssi__pack_to_buffer
                     (const RpcReqWifiStaGetRssi   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiStaGetRssi *
       rpc__req__wifi_sta_get_rssi__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_sta_get_rssi__free_unpacked
                     (RpcReqWifiStaGetRssi *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiStaGetRssi methods */
void   rpc__resp__wifi_sta_get_rssi__init
                     (RpcRespWifiStaGetRssi         *message);
size_t rpc__resp__wifi_sta_get_rssi__get_packed_size
                     (const RpcRespWifiStaGetRssi   *message);
size_t rpc__resp__wifi_sta_get_rssi__pack
                     (const RpcRespWifiStaGetRssi   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_sta_get_rssi__pack_to_buffer
                     (const RpcRespWifiStaGetRssi   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiStaGetRssi *
       rpc__resp__wifi_sta_get_rssi__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_sta_get_rssi__free_unpacked
                     (RpcRespWifiStaGetRssi *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiStaGetAid methods */
void   rpc__req__wifi_sta_get_aid__init
                     (RpcReqWifiStaGetAid         *message);
size_t rpc__req__wifi_sta_get_aid__get_packed_size
                     (const RpcReqWifiStaGetAid   *message);
size_t rpc__req__wifi_sta_get_aid__pack
                     (const RpcReqWifiStaGetAid   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_sta_get_aid__pack_to_buffer
                     (const RpcReqWifiStaGetAid   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiStaGetAid *
       rpc__req__wifi_sta_get_aid__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_sta_get_aid__free_unpacked
                     (RpcReqWifiStaGetAid *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiStaGetAid methods */
void   rpc__resp__wifi_sta_get_aid__init
                     (RpcRespWifiStaGetAid         *message);
size_t rpc__resp__wifi_sta_get_aid__get_packed_size
                     (const RpcRespWifiStaGetAid   *message);
size_t rpc__resp__wifi_sta_get_aid__pack
                     (const RpcRespWifiStaGetAid   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_sta_get_aid__pack_to_buffer
                     (const RpcRespWifiStaGetAid   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiStaGetAid *
       rpc__resp__wifi_sta_get_aid__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_sta_get_aid__free_unpacked
                     (RpcRespWifiStaGetAid *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiSetProtocols methods */
void   rpc__req__wifi_set_protocols__init
                     (RpcReqWifiSetProtocols         *message);
size_t rpc__req__wifi_set_protocols__get_packed_size
                     (const RpcReqWifiSetProtocols   *message);
size_t rpc__req__wifi_set_protocols__pack
                     (const RpcReqWifiSetProtocols   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_set_protocols__pack_to_buffer
                     (const RpcReqWifiSetProtocols   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiSetProtocols *
       rpc__req__wifi_set_protocols__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_set_protocols__free_unpacked
                     (RpcReqWifiSetProtocols *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiSetProtocols methods */
void   rpc__resp__wifi_set_protocols__init
                     (RpcRespWifiSetProtocols         *message);
size_t rpc__resp__wifi_set_protocols__get_packed_size
                     (const RpcRespWifiSetProtocols   *message);
size_t rpc__resp__wifi_set_protocols__pack
                     (const RpcRespWifiSetProtocols   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_set_protocols__pack_to_buffer
                     (const RpcRespWifiSetProtocols   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiSetProtocols *
       rpc__resp__wifi_set_protocols__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_set_protocols__free_unpacked
                     (RpcRespWifiSetProtocols *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiGetProtocols methods */
void   rpc__req__wifi_get_protocols__init
                     (RpcReqWifiGetProtocols         *message);
size_t rpc__req__wifi_get_protocols__get_packed_size
                     (const RpcReqWifiGetProtocols   *message);
size_t rpc__req__wifi_get_protocols__pack
                     (const RpcReqWifiGetProtocols   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_get_protocols__pack_to_buffer
                     (const RpcReqWifiGetProtocols   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiGetProtocols *
       rpc__req__wifi_get_protocols__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_get_protocols__free_unpacked
                     (RpcReqWifiGetProtocols *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiGetProtocols methods */
void   rpc__resp__wifi_get_protocols__init
                     (RpcRespWifiGetProtocols         *message);
size_t rpc__resp__wifi_get_protocols__get_packed_size
                     (const RpcRespWifiGetProtocols   *message);
size_t rpc__resp__wifi_get_protocols__pack
                     (const RpcRespWifiGetProtocols   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_get_protocols__pack_to_buffer
                     (const RpcRespWifiGetProtocols   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiGetProtocols *
       rpc__resp__wifi_get_protocols__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_get_protocols__free_unpacked
                     (RpcRespWifiGetProtocols *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiSetBandwidths methods */
void   rpc__req__wifi_set_bandwidths__init
                     (RpcReqWifiSetBandwidths         *message);
size_t rpc__req__wifi_set_bandwidths__get_packed_size
                     (const RpcReqWifiSetBandwidths   *message);
size_t rpc__req__wifi_set_bandwidths__pack
                     (const RpcReqWifiSetBandwidths   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_set_bandwidths__pack_to_buffer
                     (const RpcReqWifiSetBandwidths   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiSetBandwidths *
       rpc__req__wifi_set_bandwidths__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_set_bandwidths__free_unpacked
                     (RpcReqWifiSetBandwidths *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiSetBandwidths methods */
void   rpc__resp__wifi_set_bandwidths__init
                     (RpcRespWifiSetBandwidths         *message);
size_t rpc__resp__wifi_set_bandwidths__get_packed_size
                     (const RpcRespWifiSetBandwidths   *message);
size_t rpc__resp__wifi_set_bandwidths__pack
                     (const RpcRespWifiSetBandwidths   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_set_bandwidths__pack_to_buffer
                     (const RpcRespWifiSetBandwidths   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiSetBandwidths *
       rpc__resp__wifi_set_bandwidths__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_set_bandwidths__free_unpacked
                     (RpcRespWifiSetBandwidths *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiGetBandwidths methods */
void   rpc__req__wifi_get_bandwidths__init
                     (RpcReqWifiGetBandwidths         *message);
size_t rpc__req__wifi_get_bandwidths__get_packed_size
                     (const RpcReqWifiGetBandwidths   *message);
size_t rpc__req__wifi_get_bandwidths__pack
                     (const RpcReqWifiGetBandwidths   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_get_bandwidths__pack_to_buffer
                     (const RpcReqWifiGetBandwidths   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiGetBandwidths *
       rpc__req__wifi_get_bandwidths__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_get_bandwidths__free_unpacked
                     (RpcReqWifiGetBandwidths *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiGetBandwidths methods */
void   rpc__resp__wifi_get_bandwidths__init
                     (RpcRespWifiGetBandwidths         *message);
size_t rpc__resp__wifi_get_bandwidths__get_packed_size
                     (const RpcRespWifiGetBandwidths   *message);
size_t rpc__resp__wifi_get_bandwidths__pack
                     (const RpcRespWifiGetBandwidths   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_get_bandwidths__pack_to_buffer
                     (const RpcRespWifiGetBandwidths   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiGetBandwidths *
       rpc__resp__wifi_get_bandwidths__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_get_bandwidths__free_unpacked
                     (RpcRespWifiGetBandwidths *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiSetBand methods */
void   rpc__req__wifi_set_band__init
                     (RpcReqWifiSetBand         *message);
size_t rpc__req__wifi_set_band__get_packed_size
                     (const RpcReqWifiSetBand   *message);
size_t rpc__req__wifi_set_band__pack
                     (const RpcReqWifiSetBand   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_set_band__pack_to_buffer
                     (const RpcReqWifiSetBand   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiSetBand *
       rpc__req__wifi_set_band__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_set_band__free_unpacked
                     (RpcReqWifiSetBand *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiSetBand methods */
void   rpc__resp__wifi_set_band__init
                     (RpcRespWifiSetBand         *message);
size_t rpc__resp__wifi_set_band__get_packed_size
                     (const RpcRespWifiSetBand   *message);
size_t rpc__resp__wifi_set_band__pack
                     (const RpcRespWifiSetBand   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_set_band__pack_to_buffer
                     (const RpcRespWifiSetBand   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiSetBand *
       rpc__resp__wifi_set_band__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_set_band__free_unpacked
                     (RpcRespWifiSetBand *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiGetBand methods */
void   rpc__req__wifi_get_band__init
                     (RpcReqWifiGetBand         *message);
size_t rpc__req__wifi_get_band__get_packed_size
                     (const RpcReqWifiGetBand   *message);
size_t rpc__req__wifi_get_band__pack
                     (const RpcReqWifiGetBand   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_get_band__pack_to_buffer
                     (const RpcReqWifiGetBand   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiGetBand *
       rpc__req__wifi_get_band__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_get_band__free_unpacked
                     (RpcReqWifiGetBand *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiGetBand methods */
void   rpc__resp__wifi_get_band__init
                     (RpcRespWifiGetBand         *message);
size_t rpc__resp__wifi_get_band__get_packed_size
                     (const RpcRespWifiGetBand   *message);
size_t rpc__resp__wifi_get_band__pack
                     (const RpcRespWifiGetBand   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_get_band__pack_to_buffer
                     (const RpcRespWifiGetBand   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiGetBand *
       rpc__resp__wifi_get_band__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_get_band__free_unpacked
                     (RpcRespWifiGetBand *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiSetBandMode methods */
void   rpc__req__wifi_set_band_mode__init
                     (RpcReqWifiSetBandMode         *message);
size_t rpc__req__wifi_set_band_mode__get_packed_size
                     (const RpcReqWifiSetBandMode   *message);
size_t rpc__req__wifi_set_band_mode__pack
                     (const RpcReqWifiSetBandMode   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_set_band_mode__pack_to_buffer
                     (const RpcReqWifiSetBandMode   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiSetBandMode *
       rpc__req__wifi_set_band_mode__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_set_band_mode__free_unpacked
                     (RpcReqWifiSetBandMode *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiSetBandMode methods */
void   rpc__resp__wifi_set_band_mode__init
                     (RpcRespWifiSetBandMode         *message);
size_t rpc__resp__wifi_set_band_mode__get_packed_size
                     (const RpcRespWifiSetBandMode   *message);
size_t rpc__resp__wifi_set_band_mode__pack
                     (const RpcRespWifiSetBandMode   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_set_band_mode__pack_to_buffer
                     (const RpcRespWifiSetBandMode   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiSetBandMode *
       rpc__resp__wifi_set_band_mode__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_set_band_mode__free_unpacked
                     (RpcRespWifiSetBandMode *message,
                      ProtobufCAllocator *allocator);
/* RpcReqWifiGetBandMode methods */
void   rpc__req__wifi_get_band_mode__init
                     (RpcReqWifiGetBandMode         *message);
size_t rpc__req__wifi_get_band_mode__get_packed_size
                     (const RpcReqWifiGetBandMode   *message);
size_t rpc__req__wifi_get_band_mode__pack
                     (const RpcReqWifiGetBandMode   *message,
                      uint8_t             *out);
size_t rpc__req__wifi_get_band_mode__pack_to_buffer
                     (const RpcReqWifiGetBandMode   *message,
                      ProtobufCBuffer     *buffer);
RpcReqWifiGetBandMode *
       rpc__req__wifi_get_band_mode__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__req__wifi_get_band_mode__free_unpacked
                     (RpcReqWifiGetBandMode *message,
                      ProtobufCAllocator *allocator);
/* RpcRespWifiGetBandMode methods */
void   rpc__resp__wifi_get_band_mode__init
                     (RpcRespWifiGetBandMode         *message);
size_t rpc__resp__wifi_get_band_mode__get_packed_size
                     (const RpcRespWifiGetBandMode   *message);
size_t rpc__resp__wifi_get_band_mode__pack
                     (const RpcRespWifiGetBandMode   *message,
                      uint8_t             *out);
size_t rpc__resp__wifi_get_band_mode__pack_to_buffer
                     (const RpcRespWifiGetBandMode   *message,
                      ProtobufCBuffer     *buffer);
RpcRespWifiGetBandMode *
       rpc__resp__wifi_get_band_mode__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__resp__wifi_get_band_mode__free_unpacked
                     (RpcRespWifiGetBandMode *message,
                      ProtobufCAllocator *allocator);
/* RpcEventWifiEventNoArgs methods */
void   rpc__event__wifi_event_no_args__init
                     (RpcEventWifiEventNoArgs         *message);
size_t rpc__event__wifi_event_no_args__get_packed_size
                     (const RpcEventWifiEventNoArgs   *message);
size_t rpc__event__wifi_event_no_args__pack
                     (const RpcEventWifiEventNoArgs   *message,
                      uint8_t             *out);
size_t rpc__event__wifi_event_no_args__pack_to_buffer
                     (const RpcEventWifiEventNoArgs   *message,
                      ProtobufCBuffer     *buffer);
RpcEventWifiEventNoArgs *
       rpc__event__wifi_event_no_args__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__event__wifi_event_no_args__free_unpacked
                     (RpcEventWifiEventNoArgs *message,
                      ProtobufCAllocator *allocator);
/* RpcEventESPInit methods */
void   rpc__event__espinit__init
                     (RpcEventESPInit         *message);
size_t rpc__event__espinit__get_packed_size
                     (const RpcEventESPInit   *message);
size_t rpc__event__espinit__pack
                     (const RpcEventESPInit   *message,
                      uint8_t             *out);
size_t rpc__event__espinit__pack_to_buffer
                     (const RpcEventESPInit   *message,
                      ProtobufCBuffer     *buffer);
RpcEventESPInit *
       rpc__event__espinit__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__event__espinit__free_unpacked
                     (RpcEventESPInit *message,
                      ProtobufCAllocator *allocator);
/* RpcEventHeartbeat methods */
void   rpc__event__heartbeat__init
                     (RpcEventHeartbeat         *message);
size_t rpc__event__heartbeat__get_packed_size
                     (const RpcEventHeartbeat   *message);
size_t rpc__event__heartbeat__pack
                     (const RpcEventHeartbeat   *message,
                      uint8_t             *out);
size_t rpc__event__heartbeat__pack_to_buffer
                     (const RpcEventHeartbeat   *message,
                      ProtobufCBuffer     *buffer);
RpcEventHeartbeat *
       rpc__event__heartbeat__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__event__heartbeat__free_unpacked
                     (RpcEventHeartbeat *message,
                      ProtobufCAllocator *allocator);
/* RpcEventAPStaDisconnected methods */
void   rpc__event__ap__sta_disconnected__init
                     (RpcEventAPStaDisconnected         *message);
size_t rpc__event__ap__sta_disconnected__get_packed_size
                     (const RpcEventAPStaDisconnected   *message);
size_t rpc__event__ap__sta_disconnected__pack
                     (const RpcEventAPStaDisconnected   *message,
                      uint8_t             *out);
size_t rpc__event__ap__sta_disconnected__pack_to_buffer
                     (const RpcEventAPStaDisconnected   *message,
                      ProtobufCBuffer     *buffer);
RpcEventAPStaDisconnected *
       rpc__event__ap__sta_disconnected__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__event__ap__sta_disconnected__free_unpacked
                     (RpcEventAPStaDisconnected *message,
                      ProtobufCAllocator *allocator);
/* RpcEventAPStaConnected methods */
void   rpc__event__ap__sta_connected__init
                     (RpcEventAPStaConnected         *message);
size_t rpc__event__ap__sta_connected__get_packed_size
                     (const RpcEventAPStaConnected   *message);
size_t rpc__event__ap__sta_connected__pack
                     (const RpcEventAPStaConnected   *message,
                      uint8_t             *out);
size_t rpc__event__ap__sta_connected__pack_to_buffer
                     (const RpcEventAPStaConnected   *message,
                      ProtobufCBuffer     *buffer);
RpcEventAPStaConnected *
       rpc__event__ap__sta_connected__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__event__ap__sta_connected__free_unpacked
                     (RpcEventAPStaConnected *message,
                      ProtobufCAllocator *allocator);
/* RpcEventStaScanDone methods */
void   rpc__event__sta_scan_done__init
                     (RpcEventStaScanDone         *message);
size_t rpc__event__sta_scan_done__get_packed_size
                     (const RpcEventStaScanDone   *message);
size_t rpc__event__sta_scan_done__pack
                     (const RpcEventStaScanDone   *message,
                      uint8_t             *out);
size_t rpc__event__sta_scan_done__pack_to_buffer
                     (const RpcEventStaScanDone   *message,
                      ProtobufCBuffer     *buffer);
RpcEventStaScanDone *
       rpc__event__sta_scan_done__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__event__sta_scan_done__free_unpacked
                     (RpcEventStaScanDone *message,
                      ProtobufCAllocator *allocator);
/* RpcEventStaConnected methods */
void   rpc__event__sta_connected__init
                     (RpcEventStaConnected         *message);
size_t rpc__event__sta_connected__get_packed_size
                     (const RpcEventStaConnected   *message);
size_t rpc__event__sta_connected__pack
                     (const RpcEventStaConnected   *message,
                      uint8_t             *out);
size_t rpc__event__sta_connected__pack_to_buffer
                     (const RpcEventStaConnected   *message,
                      ProtobufCBuffer     *buffer);
RpcEventStaConnected *
       rpc__event__sta_connected__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__event__sta_connected__free_unpacked
                     (RpcEventStaConnected *message,
                      ProtobufCAllocator *allocator);
/* RpcEventStaDisconnected methods */
void   rpc__event__sta_disconnected__init
                     (RpcEventStaDisconnected         *message);
size_t rpc__event__sta_disconnected__get_packed_size
                     (const RpcEventStaDisconnected   *message);
size_t rpc__event__sta_disconnected__pack
                     (const RpcEventStaDisconnected   *message,
                      uint8_t             *out);
size_t rpc__event__sta_disconnected__pack_to_buffer
                     (const RpcEventStaDisconnected   *message,
                      ProtobufCBuffer     *buffer);
RpcEventStaDisconnected *
       rpc__event__sta_disconnected__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__event__sta_disconnected__free_unpacked
                     (RpcEventStaDisconnected *message,
                      ProtobufCAllocator *allocator);
/* Rpc methods */
void   rpc__init
                     (Rpc         *message);
size_t rpc__get_packed_size
                     (const Rpc   *message);
size_t rpc__pack
                     (const Rpc   *message,
                      uint8_t             *out);
size_t rpc__pack_to_buffer
                     (const Rpc   *message,
                      ProtobufCBuffer     *buffer);
Rpc *
       rpc__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rpc__free_unpacked
                     (Rpc *message,
                      ProtobufCAllocator *allocator);
/* --- per-message closures --- */

typedef void (*WifiInitConfig_Closure)
                 (const WifiInitConfig *message,
                  void *closure_data);
typedef void (*WifiCountry_Closure)
                 (const WifiCountry *message,
                  void *closure_data);
typedef void (*WifiActiveScanTime_Closure)
                 (const WifiActiveScanTime *message,
                  void *closure_data);
typedef void (*WifiScanTime_Closure)
                 (const WifiScanTime *message,
                  void *closure_data);
typedef void (*WifiScanConfig_Closure)
                 (const WifiScanConfig *message,
                  void *closure_data);
typedef void (*WifiHeApInfo_Closure)
                 (const WifiHeApInfo *message,
                  void *closure_data);
typedef void (*WifiApRecord_Closure)
                 (const WifiApRecord *message,
                  void *closure_data);
typedef void (*WifiScanThreshold_Closure)
                 (const WifiScanThreshold *message,
                  void *closure_data);
typedef void (*WifiPmfConfig_Closure)
                 (const WifiPmfConfig *message,
                  void *closure_data);
typedef void (*WifiApConfig_Closure)
                 (const WifiApConfig *message,
                  void *closure_data);
typedef void (*WifiStaConfig_Closure)
                 (const WifiStaConfig *message,
                  void *closure_data);
typedef void (*WifiConfig_Closure)
                 (const WifiConfig *message,
                  void *closure_data);
typedef void (*WifiStaInfo_Closure)
                 (const WifiStaInfo *message,
                  void *closure_data);
typedef void (*WifiStaList_Closure)
                 (const WifiStaList *message,
                  void *closure_data);
typedef void (*WifiPktRxCtrl_Closure)
                 (const WifiPktRxCtrl *message,
                  void *closure_data);
typedef void (*WifiPromiscuousPkt_Closure)
                 (const WifiPromiscuousPkt *message,
                  void *closure_data);
typedef void (*WifiPromiscuousFilter_Closure)
                 (const WifiPromiscuousFilter *message,
                  void *closure_data);
typedef void (*WifiCsiConfig_Closure)
                 (const WifiCsiConfig *message,
                  void *closure_data);
typedef void (*WifiCsiInfo_Closure)
                 (const WifiCsiInfo *message,
                  void *closure_data);
typedef void (*WifiAntGpio_Closure)
                 (const WifiAntGpio *message,
                  void *closure_data);
typedef void (*WifiAntGpioConfig_Closure)
                 (const WifiAntGpioConfig *message,
                  void *closure_data);
typedef void (*WifiAntConfig_Closure)
                 (const WifiAntConfig *message,
                  void *closure_data);
typedef void (*WifiActionTxReq_Closure)
                 (const WifiActionTxReq *message,
                  void *closure_data);
typedef void (*WifiFtmInitiatorCfg_Closure)
                 (const WifiFtmInitiatorCfg *message,
                  void *closure_data);
typedef void (*WifiEventStaScanDone_Closure)
                 (const WifiEventStaScanDone *message,
                  void *closure_data);
typedef void (*WifiEventStaConnected_Closure)
                 (const WifiEventStaConnected *message,
                  void *closure_data);
typedef void (*WifiEventStaDisconnected_Closure)
                 (const WifiEventStaDisconnected *message,
                  void *closure_data);
typedef void (*WifiEventStaAuthmodeChange_Closure)
                 (const WifiEventStaAuthmodeChange *message,
                  void *closure_data);
typedef void (*WifiEventStaWpsErPin_Closure)
                 (const WifiEventStaWpsErPin *message,
                  void *closure_data);
typedef void (*ApCred_Closure)
                 (const ApCred *message,
                  void *closure_data);
typedef void (*WifiEventStaWpsErSuccess_Closure)
                 (const WifiEventStaWpsErSuccess *message,
                  void *closure_data);
typedef void (*WifiEventApProbeReqRx_Closure)
                 (const WifiEventApProbeReqRx *message,
                  void *closure_data);
typedef void (*WifiEventBssRssiLow_Closure)
                 (const WifiEventBssRssiLow *message,
                  void *closure_data);
typedef void (*WifiFtmReportEntry_Closure)
                 (const WifiFtmReportEntry *message,
                  void *closure_data);
typedef void (*WifiEventFtmReport_Closure)
                 (const WifiEventFtmReport *message,
                  void *closure_data);
typedef void (*WifiEventActionTxStatus_Closure)
                 (const WifiEventActionTxStatus *message,
                  void *closure_data);
typedef void (*WifiEventRocDone_Closure)
                 (const WifiEventRocDone *message,
                  void *closure_data);
typedef void (*WifiEventApWpsRgPin_Closure)
                 (const WifiEventApWpsRgPin *message,
                  void *closure_data);
typedef void (*WifiEventApWpsRgFailReason_Closure)
                 (const WifiEventApWpsRgFailReason *message,
                  void *closure_data);
typedef void (*WifiEventApWpsRgSuccess_Closure)
                 (const WifiEventApWpsRgSuccess *message,
                  void *closure_data);
typedef void (*WifiProtocols_Closure)
                 (const WifiProtocols *message,
                  void *closure_data);
typedef void (*WifiBandwidths_Closure)
                 (const WifiBandwidths *message,
                  void *closure_data);
typedef void (*ConnectedSTAList_Closure)
                 (const ConnectedSTAList *message,
                  void *closure_data);
typedef void (*RpcReqGetMacAddress_Closure)
                 (const RpcReqGetMacAddress *message,
                  void *closure_data);
typedef void (*RpcRespGetMacAddress_Closure)
                 (const RpcRespGetMacAddress *message,
                  void *closure_data);
typedef void (*RpcReqGetMode_Closure)
                 (const RpcReqGetMode *message,
                  void *closure_data);
typedef void (*RpcRespGetMode_Closure)
                 (const RpcRespGetMode *message,
                  void *closure_data);
typedef void (*RpcReqSetMode_Closure)
                 (const RpcReqSetMode *message,
                  void *closure_data);
typedef void (*RpcRespSetMode_Closure)
                 (const RpcRespSetMode *message,
                  void *closure_data);
typedef void (*RpcReqGetPs_Closure)
                 (const RpcReqGetPs *message,
                  void *closure_data);
typedef void (*RpcRespGetPs_Closure)
                 (const RpcRespGetPs *message,
                  void *closure_data);
typedef void (*RpcReqSetPs_Closure)
                 (const RpcReqSetPs *message,
                  void *closure_data);
typedef void (*RpcRespSetPs_Closure)
                 (const RpcRespSetPs *message,
                  void *closure_data);
typedef void (*RpcReqSetMacAddress_Closure)
                 (const RpcReqSetMacAddress *message,
                  void *closure_data);
typedef void (*RpcRespSetMacAddress_Closure)
                 (const RpcRespSetMacAddress *message,
                  void *closure_data);
typedef void (*RpcReqOTABegin_Closure)
                 (const RpcReqOTABegin *message,
                  void *closure_data);
typedef void (*RpcRespOTABegin_Closure)
                 (const RpcRespOTABegin *message,
                  void *closure_data);
typedef void (*RpcReqOTAWrite_Closure)
                 (const RpcReqOTAWrite *message,
                  void *closure_data);
typedef void (*RpcRespOTAWrite_Closure)
                 (const RpcRespOTAWrite *message,
                  void *closure_data);
typedef void (*RpcReqOTAEnd_Closure)
                 (const RpcReqOTAEnd *message,
                  void *closure_data);
typedef void (*RpcRespOTAEnd_Closure)
                 (const RpcRespOTAEnd *message,
                  void *closure_data);
typedef void (*RpcReqWifiSetMaxTxPower_Closure)
                 (const RpcReqWifiSetMaxTxPower *message,
                  void *closure_data);
typedef void (*RpcRespWifiSetMaxTxPower_Closure)
                 (const RpcRespWifiSetMaxTxPower *message,
                  void *closure_data);
typedef void (*RpcReqWifiGetMaxTxPower_Closure)
                 (const RpcReqWifiGetMaxTxPower *message,
                  void *closure_data);
typedef void (*RpcRespWifiGetMaxTxPower_Closure)
                 (const RpcRespWifiGetMaxTxPower *message,
                  void *closure_data);
typedef void (*RpcReqConfigHeartbeat_Closure)
                 (const RpcReqConfigHeartbeat *message,
                  void *closure_data);
typedef void (*RpcRespConfigHeartbeat_Closure)
                 (const RpcRespConfigHeartbeat *message,
                  void *closure_data);
typedef void (*RpcReqWifiInit_Closure)
                 (const RpcReqWifiInit *message,
                  void *closure_data);
typedef void (*RpcRespWifiInit_Closure)
                 (const RpcRespWifiInit *message,
                  void *closure_data);
typedef void (*RpcReqWifiDeinit_Closure)
                 (const RpcReqWifiDeinit *message,
                  void *closure_data);
typedef void (*RpcRespWifiDeinit_Closure)
                 (const RpcRespWifiDeinit *message,
                  void *closure_data);
typedef void (*RpcReqWifiSetConfig_Closure)
                 (const RpcReqWifiSetConfig *message,
                  void *closure_data);
typedef void (*RpcRespWifiSetConfig_Closure)
                 (const RpcRespWifiSetConfig *message,
                  void *closure_data);
typedef void (*RpcReqWifiGetConfig_Closure)
                 (const RpcReqWifiGetConfig *message,
                  void *closure_data);
typedef void (*RpcRespWifiGetConfig_Closure)
                 (const RpcRespWifiGetConfig *message,
                  void *closure_data);
typedef void (*RpcReqWifiConnect_Closure)
                 (const RpcReqWifiConnect *message,
                  void *closure_data);
typedef void (*RpcRespWifiConnect_Closure)
                 (const RpcRespWifiConnect *message,
                  void *closure_data);
typedef void (*RpcReqWifiDisconnect_Closure)
                 (const RpcReqWifiDisconnect *message,
                  void *closure_data);
typedef void (*RpcRespWifiDisconnect_Closure)
                 (const RpcRespWifiDisconnect *message,
                  void *closure_data);
typedef void (*RpcReqWifiStart_Closure)
                 (const RpcReqWifiStart *message,
                  void *closure_data);
typedef void (*RpcRespWifiStart_Closure)
                 (const RpcRespWifiStart *message,
                  void *closure_data);
typedef void (*RpcReqWifiStop_Closure)
                 (const RpcReqWifiStop *message,
                  void *closure_data);
typedef void (*RpcRespWifiStop_Closure)
                 (const RpcRespWifiStop *message,
                  void *closure_data);
typedef void (*RpcReqWifiScanStart_Closure)
                 (const RpcReqWifiScanStart *message,
                  void *closure_data);
typedef void (*RpcRespWifiScanStart_Closure)
                 (const RpcRespWifiScanStart *message,
                  void *closure_data);
typedef void (*RpcReqWifiScanStop_Closure)
                 (const RpcReqWifiScanStop *message,
                  void *closure_data);
typedef void (*RpcRespWifiScanStop_Closure)
                 (const RpcRespWifiScanStop *message,
                  void *closure_data);
typedef void (*RpcReqWifiScanGetApNum_Closure)
                 (const RpcReqWifiScanGetApNum *message,
                  void *closure_data);
typedef void (*RpcRespWifiScanGetApNum_Closure)
                 (const RpcRespWifiScanGetApNum *message,
                  void *closure_data);
typedef void (*RpcReqWifiScanGetApRecords_Closure)
                 (const RpcReqWifiScanGetApRecords *message,
                  void *closure_data);
typedef void (*RpcRespWifiScanGetApRecords_Closure)
                 (const RpcRespWifiScanGetApRecords *message,
                  void *closure_data);
typedef void (*RpcReqWifiClearApList_Closure)
                 (const RpcReqWifiClearApList *message,
                  void *closure_data);
typedef void (*RpcRespWifiClearApList_Closure)
                 (const RpcRespWifiClearApList *message,
                  void *closure_data);
typedef void (*RpcReqWifiRestore_Closure)
                 (const RpcReqWifiRestore *message,
                  void *closure_data);
typedef void (*RpcRespWifiRestore_Closure)
                 (const RpcRespWifiRestore *message,
                  void *closure_data);
typedef void (*RpcReqWifiClearFastConnect_Closure)
                 (const RpcReqWifiClearFastConnect *message,
                  void *closure_data);
typedef void (*RpcRespWifiClearFastConnect_Closure)
                 (const RpcRespWifiClearFastConnect *message,
                  void *closure_data);
typedef void (*RpcReqWifiDeauthSta_Closure)
                 (const RpcReqWifiDeauthSta *message,
                  void *closure_data);
typedef void (*RpcRespWifiDeauthSta_Closure)
                 (const RpcRespWifiDeauthSta *message,
                  void *closure_data);
typedef void (*RpcReqWifiStaGetApInfo_Closure)
                 (const RpcReqWifiStaGetApInfo *message,
                  void *closure_data);
typedef void (*RpcRespWifiStaGetApInfo_Closure)
                 (const RpcRespWifiStaGetApInfo *message,
                  void *closure_data);
typedef void (*RpcReqWifiSetProtocol_Closure)
                 (const RpcReqWifiSetProtocol *message,
                  void *closure_data);
typedef void (*RpcRespWifiSetProtocol_Closure)
                 (const RpcRespWifiSetProtocol *message,
                  void *closure_data);
typedef void (*RpcReqWifiGetProtocol_Closure)
                 (const RpcReqWifiGetProtocol *message,
                  void *closure_data);
typedef void (*RpcRespWifiGetProtocol_Closure)
                 (const RpcRespWifiGetProtocol *message,
                  void *closure_data);
typedef void (*RpcReqWifiSetBandwidth_Closure)
                 (const RpcReqWifiSetBandwidth *message,
                  void *closure_data);
typedef void (*RpcRespWifiSetBandwidth_Closure)
                 (const RpcRespWifiSetBandwidth *message,
                  void *closure_data);
typedef void (*RpcReqWifiGetBandwidth_Closure)
                 (const RpcReqWifiGetBandwidth *message,
                  void *closure_data);
typedef void (*RpcRespWifiGetBandwidth_Closure)
                 (const RpcRespWifiGetBandwidth *message,
                  void *closure_data);
typedef void (*RpcReqWifiSetChannel_Closure)
                 (const RpcReqWifiSetChannel *message,
                  void *closure_data);
typedef void (*RpcRespWifiSetChannel_Closure)
                 (const RpcRespWifiSetChannel *message,
                  void *closure_data);
typedef void (*RpcReqWifiGetChannel_Closure)
                 (const RpcReqWifiGetChannel *message,
                  void *closure_data);
typedef void (*RpcRespWifiGetChannel_Closure)
                 (const RpcRespWifiGetChannel *message,
                  void *closure_data);
typedef void (*RpcReqWifiSetStorage_Closure)
                 (const RpcReqWifiSetStorage *message,
                  void *closure_data);
typedef void (*RpcRespWifiSetStorage_Closure)
                 (const RpcRespWifiSetStorage *message,
                  void *closure_data);
typedef void (*RpcReqWifiSetCountryCode_Closure)
                 (const RpcReqWifiSetCountryCode *message,
                  void *closure_data);
typedef void (*RpcRespWifiSetCountryCode_Closure)
                 (const RpcRespWifiSetCountryCode *message,
                  void *closure_data);
typedef void (*RpcReqWifiGetCountryCode_Closure)
                 (const RpcReqWifiGetCountryCode *message,
                  void *closure_data);
typedef void (*RpcRespWifiGetCountryCode_Closure)
                 (const RpcRespWifiGetCountryCode *message,
                  void *closure_data);
typedef void (*RpcReqWifiSetCountry_Closure)
                 (const RpcReqWifiSetCountry *message,
                  void *closure_data);
typedef void (*RpcRespWifiSetCountry_Closure)
                 (const RpcRespWifiSetCountry *message,
                  void *closure_data);
typedef void (*RpcReqWifiGetCountry_Closure)
                 (const RpcReqWifiGetCountry *message,
                  void *closure_data);
typedef void (*RpcRespWifiGetCountry_Closure)
                 (const RpcRespWifiGetCountry *message,
                  void *closure_data);
typedef void (*RpcReqWifiApGetStaList_Closure)
                 (const RpcReqWifiApGetStaList *message,
                  void *closure_data);
typedef void (*RpcRespWifiApGetStaList_Closure)
                 (const RpcRespWifiApGetStaList *message,
                  void *closure_data);
typedef void (*RpcReqWifiApGetStaAid_Closure)
                 (const RpcReqWifiApGetStaAid *message,
                  void *closure_data);
typedef void (*RpcRespWifiApGetStaAid_Closure)
                 (const RpcRespWifiApGetStaAid *message,
                  void *closure_data);
typedef void (*RpcReqWifiStaGetRssi_Closure)
                 (const RpcReqWifiStaGetRssi *message,
                  void *closure_data);
typedef void (*RpcRespWifiStaGetRssi_Closure)
                 (const RpcRespWifiStaGetRssi *message,
                  void *closure_data);
typedef void (*RpcReqWifiStaGetAid_Closure)
                 (const RpcReqWifiStaGetAid *message,
                  void *closure_data);
typedef void (*RpcRespWifiStaGetAid_Closure)
                 (const RpcRespWifiStaGetAid *message,
                  void *closure_data);
typedef void (*RpcReqWifiSetProtocols_Closure)
                 (const RpcReqWifiSetProtocols *message,
                  void *closure_data);
typedef void (*RpcRespWifiSetProtocols_Closure)
                 (const RpcRespWifiSetProtocols *message,
                  void *closure_data);
typedef void (*RpcReqWifiGetProtocols_Closure)
                 (const RpcReqWifiGetProtocols *message,
                  void *closure_data);
typedef void (*RpcRespWifiGetProtocols_Closure)
                 (const RpcRespWifiGetProtocols *message,
                  void *closure_data);
typedef void (*RpcReqWifiSetBandwidths_Closure)
                 (const RpcReqWifiSetBandwidths *message,
                  void *closure_data);
typedef void (*RpcRespWifiSetBandwidths_Closure)
                 (const RpcRespWifiSetBandwidths *message,
                  void *closure_data);
typedef void (*RpcReqWifiGetBandwidths_Closure)
                 (const RpcReqWifiGetBandwidths *message,
                  void *closure_data);
typedef void (*RpcRespWifiGetBandwidths_Closure)
                 (const RpcRespWifiGetBandwidths *message,
                  void *closure_data);
typedef void (*RpcReqWifiSetBand_Closure)
                 (const RpcReqWifiSetBand *message,
                  void *closure_data);
typedef void (*RpcRespWifiSetBand_Closure)
                 (const RpcRespWifiSetBand *message,
                  void *closure_data);
typedef void (*RpcReqWifiGetBand_Closure)
                 (const RpcReqWifiGetBand *message,
                  void *closure_data);
typedef void (*RpcRespWifiGetBand_Closure)
                 (const RpcRespWifiGetBand *message,
                  void *closure_data);
typedef void (*RpcReqWifiSetBandMode_Closure)
                 (const RpcReqWifiSetBandMode *message,
                  void *closure_data);
typedef void (*RpcRespWifiSetBandMode_Closure)
                 (const RpcRespWifiSetBandMode *message,
                  void *closure_data);
typedef void (*RpcReqWifiGetBandMode_Closure)
                 (const RpcReqWifiGetBandMode *message,
                  void *closure_data);
typedef void (*RpcRespWifiGetBandMode_Closure)
                 (const RpcRespWifiGetBandMode *message,
                  void *closure_data);
typedef void (*RpcEventWifiEventNoArgs_Closure)
                 (const RpcEventWifiEventNoArgs *message,
                  void *closure_data);
typedef void (*RpcEventESPInit_Closure)
                 (const RpcEventESPInit *message,
                  void *closure_data);
typedef void (*RpcEventHeartbeat_Closure)
                 (const RpcEventHeartbeat *message,
                  void *closure_data);
typedef void (*RpcEventAPStaDisconnected_Closure)
                 (const RpcEventAPStaDisconnected *message,
                  void *closure_data);
typedef void (*RpcEventAPStaConnected_Closure)
                 (const RpcEventAPStaConnected *message,
                  void *closure_data);
typedef void (*RpcEventStaScanDone_Closure)
                 (const RpcEventStaScanDone *message,
                  void *closure_data);
typedef void (*RpcEventStaConnected_Closure)
                 (const RpcEventStaConnected *message,
                  void *closure_data);
typedef void (*RpcEventStaDisconnected_Closure)
                 (const RpcEventStaDisconnected *message,
                  void *closure_data);
typedef void (*Rpc_Closure)
                 (const Rpc *message,
                  void *closure_data);

/* --- services --- */


/* --- descriptors --- */

extern const ProtobufCEnumDescriptor    rpc__wifi_bw__descriptor;
extern const ProtobufCEnumDescriptor    rpc__wifi_power_save__descriptor;
extern const ProtobufCEnumDescriptor    rpc__wifi_sec_prot__descriptor;
extern const ProtobufCEnumDescriptor    rpc__status__descriptor;
extern const ProtobufCEnumDescriptor    rpc_type__descriptor;
extern const ProtobufCEnumDescriptor    rpc_id__descriptor;
extern const ProtobufCMessageDescriptor wifi_init_config__descriptor;
extern const ProtobufCMessageDescriptor wifi_country__descriptor;
extern const ProtobufCMessageDescriptor wifi_active_scan_time__descriptor;
extern const ProtobufCMessageDescriptor wifi_scan_time__descriptor;
extern const ProtobufCMessageDescriptor wifi_scan_config__descriptor;
extern const ProtobufCMessageDescriptor wifi_he_ap_info__descriptor;
extern const ProtobufCMessageDescriptor wifi_ap_record__descriptor;
extern const ProtobufCMessageDescriptor wifi_scan_threshold__descriptor;
extern const ProtobufCMessageDescriptor wifi_pmf_config__descriptor;
extern const ProtobufCMessageDescriptor wifi_ap_config__descriptor;
extern const ProtobufCMessageDescriptor wifi_sta_config__descriptor;
extern const ProtobufCMessageDescriptor wifi_config__descriptor;
extern const ProtobufCMessageDescriptor wifi_sta_info__descriptor;
extern const ProtobufCMessageDescriptor wifi_sta_list__descriptor;
extern const ProtobufCMessageDescriptor wifi_pkt_rx_ctrl__descriptor;
extern const ProtobufCMessageDescriptor wifi_promiscuous_pkt__descriptor;
extern const ProtobufCMessageDescriptor wifi_promiscuous_filter__descriptor;
extern const ProtobufCMessageDescriptor wifi_csi_config__descriptor;
extern const ProtobufCMessageDescriptor wifi_csi_info__descriptor;
extern const ProtobufCMessageDescriptor wifi_ant_gpio__descriptor;
extern const ProtobufCMessageDescriptor wifi_ant_gpio_config__descriptor;
extern const ProtobufCMessageDescriptor wifi_ant_config__descriptor;
extern const ProtobufCMessageDescriptor wifi_action_tx_req__descriptor;
extern const ProtobufCMessageDescriptor wifi_ftm_initiator_cfg__descriptor;
extern const ProtobufCMessageDescriptor wifi_event_sta_scan_done__descriptor;
extern const ProtobufCMessageDescriptor wifi_event_sta_connected__descriptor;
extern const ProtobufCMessageDescriptor wifi_event_sta_disconnected__descriptor;
extern const ProtobufCMessageDescriptor wifi_event_sta_authmode_change__descriptor;
extern const ProtobufCMessageDescriptor wifi_event_sta_wps_er_pin__descriptor;
extern const ProtobufCMessageDescriptor ap_cred__descriptor;
extern const ProtobufCMessageDescriptor wifi_event_sta_wps_er_success__descriptor;
extern const ProtobufCMessageDescriptor wifi_event_ap_probe_req_rx__descriptor;
extern const ProtobufCMessageDescriptor wifi_event_bss_rssi_low__descriptor;
extern const ProtobufCMessageDescriptor wifi_ftm_report_entry__descriptor;
extern const ProtobufCMessageDescriptor wifi_event_ftm_report__descriptor;
extern const ProtobufCMessageDescriptor wifi_event_action_tx_status__descriptor;
extern const ProtobufCMessageDescriptor wifi_event_roc_done__descriptor;
extern const ProtobufCMessageDescriptor wifi_event_ap_wps_rg_pin__descriptor;
extern const ProtobufCMessageDescriptor wifi_event_ap_wps_rg_fail_reason__descriptor;
extern const ProtobufCMessageDescriptor wifi_event_ap_wps_rg_success__descriptor;
extern const ProtobufCMessageDescriptor wifi_protocols__descriptor;
extern const ProtobufCMessageDescriptor wifi_bandwidths__descriptor;
extern const ProtobufCMessageDescriptor connected_stalist__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__get_mac_address__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__get_mac_address__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__get_mode__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__get_mode__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__set_mode__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__set_mode__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__get_ps__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__get_ps__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__set_ps__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__set_ps__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__set_mac_address__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__set_mac_address__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__otabegin__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__otabegin__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__otawrite__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__otawrite__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__otaend__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__otaend__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_set_max_tx_power__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_set_max_tx_power__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_get_max_tx_power__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_get_max_tx_power__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__config_heartbeat__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__config_heartbeat__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_init__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_init__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_deinit__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_deinit__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_set_config__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_set_config__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_get_config__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_get_config__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_connect__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_connect__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_disconnect__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_disconnect__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_start__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_start__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_stop__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_stop__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_scan_start__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_scan_start__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_scan_stop__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_scan_stop__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_scan_get_ap_num__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_scan_get_ap_num__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_scan_get_ap_records__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_scan_get_ap_records__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_clear_ap_list__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_clear_ap_list__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_restore__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_restore__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_clear_fast_connect__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_clear_fast_connect__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_deauth_sta__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_deauth_sta__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_sta_get_ap_info__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_sta_get_ap_info__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_set_protocol__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_set_protocol__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_get_protocol__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_get_protocol__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_set_bandwidth__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_set_bandwidth__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_get_bandwidth__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_get_bandwidth__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_set_channel__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_set_channel__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_get_channel__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_get_channel__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_set_storage__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_set_storage__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_set_country_code__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_set_country_code__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_get_country_code__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_get_country_code__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_set_country__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_set_country__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_get_country__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_get_country__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_ap_get_sta_list__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_ap_get_sta_list__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_ap_get_sta_aid__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_ap_get_sta_aid__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_sta_get_rssi__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_sta_get_rssi__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_sta_get_aid__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_sta_get_aid__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_set_protocols__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_set_protocols__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_get_protocols__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_get_protocols__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_set_bandwidths__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_set_bandwidths__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_get_bandwidths__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_get_bandwidths__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_set_band__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_set_band__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_get_band__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_get_band__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_set_band_mode__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_set_band_mode__descriptor;
extern const ProtobufCMessageDescriptor rpc__req__wifi_get_band_mode__descriptor;
extern const ProtobufCMessageDescriptor rpc__resp__wifi_get_band_mode__descriptor;
extern const ProtobufCMessageDescriptor rpc__event__wifi_event_no_args__descriptor;
extern const ProtobufCMessageDescriptor rpc__event__espinit__descriptor;
extern const ProtobufCMessageDescriptor rpc__event__heartbeat__descriptor;
extern const ProtobufCMessageDescriptor rpc__event__ap__sta_disconnected__descriptor;
extern const ProtobufCMessageDescriptor rpc__event__ap__sta_connected__descriptor;
extern const ProtobufCMessageDescriptor rpc__event__sta_scan_done__descriptor;
extern const ProtobufCMessageDescriptor rpc__event__sta_connected__descriptor;
extern const ProtobufCMessageDescriptor rpc__event__sta_disconnected__descriptor;
extern const ProtobufCMessageDescriptor rpc__descriptor;

PROTOBUF_C__END_DECLS


#endif  /* PROTOBUF_C_esp_5fhosted_5frpc_2eproto__INCLUDED */

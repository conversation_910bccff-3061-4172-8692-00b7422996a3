menu "Example Configuration"

	choice ESP_HOST_DEV_BOARD
		bool "Configure GPIOs based on an ESP Development Board"
		depends on IDF_TARGET_ESP32C6
		default ESP_HOST_DEV_BOARD_NONE
		help
			"Preconfigures GPIOs to use based on an ESP Development Board"

		config ESP_HOST_DEV_BOARD_NONE
			bool "No specific development board"

		config ESP_HOST_DEV_BOARD_P4_FUNC_BOARD
			bool "ESP32-P4-Function-EV-Board"
			depends on IDF_TARGET_ESP32C6

	endchoice

	choice ESP_HOST_INTERFACE
		bool "Transport layer"
		default ESP_SPI_HOST_INTERFACE if IDF_TARGET_ESP32
		default ESP_SPI_HOST_INTERFACE if IDF_TARGET_ESP32S2
		default ESP_SPI_HOST_INTERFACE if IDF_TARGET_ESP32S3
		default ESP_SPI_HOST_INTERFACE if IDF_TARGET_ESP32C2
		default ESP_SPI_HOST_INTERFACE if IDF_TARGET_ESP32C3
		default ESP_SDIO_HOST_INTERFACE if IDF_TARGET_ESP32C6
		default ESP_SPI_HOST_INTERFACE if IDF_TARGET_ESP32C5
		help
			Bus interface to be used for communication with the host

		config ESP_SPI_HOST_INTERFACE
			bool "SPI Full-duplex"
			help
				Enable/Disable SPI Full-duplex host interface

		config ESP_SDIO_HOST_INTERFACE
			bool "SDIO"
			depends on IDF_TARGET_ESP32 || IDF_TARGET_ESP32C6
			help
				Enable/Disable SDIO host interface

		# SPI Half Duplex is not supported in ESP32
		config ESP_SPI_HD_HOST_INTERFACE
			bool "SPI Half-duplex"
			depends on !IDF_TARGET_ESP32
			help
				Enable/Disable SPI Half-duplex host interface

		config ESP_UART_HOST_INTERFACE
			bool "UART"
			help
				Enable/Disable UART host interface
	endchoice

	menu "SPI Full-duplex Configuration"
		depends on ESP_SPI_HOST_INTERFACE

		choice ESP_SPI_PRIV_MODE
			bool "Slave SPI mode"
			default ESP_SPI_PRIV_MODE_2 if IDF_TARGET_ESP32
			default ESP_SPI_PRIV_MODE_3

			config ESP_SPI_PRIV_MODE_0
				bool "Slave SPI mode 0"

			config ESP_SPI_PRIV_MODE_1
				bool "Slave SPI mode 1"

			config ESP_SPI_PRIV_MODE_2
				bool "Slave SPI mode 2"

			config ESP_SPI_PRIV_MODE_3
				bool "Slave SPI mode 3"
		endchoice

		config ESP_SPI_MODE
			int
			default 0 if ESP_SPI_PRIV_MODE_0
			default 1 if ESP_SPI_PRIV_MODE_1
			default 2 if ESP_SPI_PRIV_MODE_2
			default 3 if ESP_SPI_PRIV_MODE_3
			default 3


		choice SPI_CONTROLLER
			bool "SPI controller to use"
			default SPI_HSPI

			config SPI_HSPI
				bool "FSPI/HSPI"
				help
					"HSPI/FSPI: SPI_controller_1"

			config SPI_VSPI
				depends on IDF_TARGET_ESP32
				bool "VSPI"
				help
					"VSPI: SPI_controller_2"

		endchoice

		config ESP_SPI_CONTROLLER
			int
			default 2 if SPI_VSPI
			default 1

		menu "Hosted SPI GPIOs"
			config ESP_SPI_HSPI_GPIO_MOSI
				depends on SPI_HSPI
				int "Slave GPIO pin for Host MOSI"
				default 20 if ESP_HOST_DEV_BOARD_P4_FUNC_BOARD
				default 13 if IDF_TARGET_ESP32
				default 11 if IDF_TARGET_ESP32S2 || IDF_TARGET_ESP32S3
				default 7
				help
					SPI controller Host MOSI

			config ESP_SPI_HSPI_GPIO_MISO
				depends on SPI_HSPI
				int "Slave GPIO pin for Host MISO"
				default 21 if ESP_HOST_DEV_BOARD_P4_FUNC_BOARD
				default 12 if IDF_TARGET_ESP32
				default 13 if IDF_TARGET_ESP32S2 || IDF_TARGET_ESP32S3
				default 2
				help
					SPI controller Host MISO

			config ESP_SPI_HSPI_GPIO_CLK
				depends on SPI_HSPI
				int "Slave GPIO pin for Host CLK"
				default 19 if ESP_HOST_DEV_BOARD_P4_FUNC_BOARD
				default 14 if IDF_TARGET_ESP32
				default 12 if IDF_TARGET_ESP32S2 || IDF_TARGET_ESP32S3
				default 6
				help
					SPI controller Host CLK

			config ESP_SPI_HSPI_GPIO_CS
				depends on SPI_HSPI
				int "Slave GPIO pin for Host  CS"
				default 18 if ESP_HOST_DEV_BOARD_P4_FUNC_BOARD
				default 15 if IDF_TARGET_ESP32
				default 10 if IDF_TARGET_ESP32S2 || IDF_TARGET_ESP32S3
				default 10
				help
					SPI controller Host CS

			config ESP_SPI_VSPI_GPIO_MOSI
				depends on SPI_VSPI
				int "Slave GPIO pin for Host MOSI"
				default 23
				help
					SPI controller Host MOSI

			config ESP_SPI_VSPI_GPIO_MISO
				depends on SPI_VSPI
				int "Slave GPIO pin for Host MISO"
				default 19
				help
					SPI controller Host MISO

			config ESP_SPI_VSPI_GPIO_CLK
				depends on SPI_VSPI
				int "Slave GPIO pin for Host CLK"
				default 18
				help
					SPI controller Host CLK

			config ESP_SPI_VSPI_GPIO_CS
				depends on SPI_VSPI
				int "Slave GPIO pin for Host CS"
				default 5
				help
					SPI controller Host CS

			config ESP_SPI_GPIO_MOSI
				int
				default ESP_SPI_VSPI_GPIO_MOSI if SPI_VSPI
				default ESP_SPI_HSPI_GPIO_MOSI

			config ESP_SPI_GPIO_MISO
				int
				default ESP_SPI_VSPI_GPIO_MISO if SPI_VSPI
				default ESP_SPI_HSPI_GPIO_MISO

			config ESP_SPI_GPIO_CLK
				int
				default ESP_SPI_VSPI_GPIO_CLK if SPI_VSPI
				default ESP_SPI_HSPI_GPIO_CLK

			config ESP_SPI_GPIO_CS
				int
				default ESP_SPI_VSPI_GPIO_CS if SPI_VSPI
				default ESP_SPI_HSPI_GPIO_CS

			config ESP_SPI_GPIO_HANDSHAKE
				int "Slave GPIO pin for handshake"
				default 22 if ESP_HOST_DEV_BOARD_P4_FUNC_BOARD
				default 3 if IDF_TARGET_ESP32C2 || IDF_TARGET_ESP32C3 || IDF_TARGET_ESP32C6
				default 17 if IDF_TARGET_ESP32S3 || IDF_TARGET_ESP32S2
				default 26
				help
					Slave GPIO pin to use for handshake with other spi controller

			config ESP_SPI_GPIO_DATA_READY
				int "Slave GPIO pin for data ready interrupt"
				default 23 if ESP_HOST_DEV_BOARD_P4_FUNC_BOARD
				default 13 if IDF_TARGET_ESP32C5
				default 4
				help
					Slave GPIO pin for indicating host that SPI slave has data to be read by host

			config ESP_SPI_GPIO_RESET
				int "Slave GPIO pin to reset itself"
				default -1
				help
					Host uses this pin to reset the slave ESP. To re-use ESP 'RST' or 'EN' GPIO, set value to -1
		endmenu

		config ESP_SPI_TX_Q_SIZE
			int "ESP to Host SPI queue size"
			default 10 if IDF_TARGET_ESP32
			default 6 if IDF_TARGET_ESP32C2 && BT_ENABLED
			default 10 if IDF_TARGET_ESP32C2
			default 20
			help
				Very small tx queue will lower ESP -- SPI --> Host data rate

		config ESP_SPI_RX_Q_SIZE
			int "Host to ESP SPI queue size"
			default 10 if IDF_TARGET_ESP32
			default 6 if IDF_TARGET_ESP32C2 && BT_ENABLED
			default 7 if IDF_TARGET_ESP32C2
			default 20
			help
				Very small RX queue will lower ESP <-- SPI -- Host data rate

		config ESP_SPI_CHECKSUM
			bool "SPI checksum ENABLE/DISABLE"
			default y
			help
				ENABLE/DISABLE software SPI checksum
	endmenu

	menu "SDIO Configuration"
		depends on ESP_SDIO_HOST_INTERFACE

		config ESP_SDIO_STREAMING_MODE
			bool "Enable SDIO Streaming Mode"
			default y
			help
				Enable Streaming Mode. Host to receive queued data from slave
				as one stream instead of individual packets. This can improve
				host SDIO read performance by doing one large read transaction
				instead of many smaller read transactions.

		config ESP_SDIO_GPIO_RESET
			int "Slave GPIO pin to reset itself"
			default -1
			help
				Host uses this pin to reset the slave ESP. To re-use ESP 'RST' or 'EN' GPIO, set value to -1

		choice
			prompt "SDIO Bus Speed"
			default ESP_SDIO_HIGH_SPEED
			help
				Select the SDIO Slave Bus Speed. Actual speed in use depends on SDIO bus speed the SDIO Master can support

			config ESP_SDIO_DEFAULT_SPEED
				bool "Default Speed (20 MHz)"

			config ESP_SDIO_HIGH_SPEED
				bool "High Speed (40 MHz)"
		endchoice


		menu "Hosted SDIO GPIOs"
			config ESP_SDIO_PIN_CMD
				int "CMD GPIO number"
				range 15 15 if IDF_TARGET_ESP32
				range 18 18 if IDF_TARGET_ESP32C6
				help
					"Value cannot be configured. Displayed for reference."

			config ESP_SDIO_PIN_CLK
				int "CLK GPIO number"
				range 14 14 if IDF_TARGET_ESP32
				range 19 19 if IDF_TARGET_ESP32C6
				help
					"Value cannot be configured. Displayed for reference."

			config ESP_SDIO_PIN_D0
				int "D0 GPIO number"
				range 2 2 if IDF_TARGET_ESP32
				range 20 20 if IDF_TARGET_ESP32C6
				help
					"Value cannot be configured. Displayed for reference."

			config ESP_SDIO_PIN_D1
				int "D1 GPIO number"
				range 4 4 if IDF_TARGET_ESP32
				range 21 21 if IDF_TARGET_ESP32C6
				help
					"Value cannot be configured. Displayed for reference."

			config ESP_SDIO_PIN_D2
				int "D2 GPIO number"
				range 12 12 if IDF_TARGET_ESP32
				range 22 22 if IDF_TARGET_ESP32C6
				help
					"Value cannot be configured. Displayed for reference."

			config ESP_SDIO_PIN_D3
				int "D3 GPIO number"
				range 13 13 if IDF_TARGET_ESP32
				range 23 23 if IDF_TARGET_ESP32C6
				help
					"Value cannot be configured. Displayed for reference."
		endmenu

		choice
			prompt "SDIO Slave Timing"
			default ESP_SDIO_PSEND_PSAMPLE
			help
				Select the SDIO timing used by slave. Default value works with most
				SDMMC controllers but if transfer errors are encountered, selecting a
				different timing may help resolve the errors.
				See https://docs.espressif.com/projects/esp-idf/en/latest/esp32/api-reference/peripherals/sdio_slave.html#_CPPv419sdio_slave_timing_t
				for more information

			config ESP_SDIO_PSEND_PSAMPLE
				bool "Send at positive edge, sample at positive edge"

			config ESP_SDIO_NSEND_PSAMPLE
				bool "Send at negative edge, sample at positive edge"

			config ESP_SDIO_PSEND_NSAMPLE
				bool "Send at positive edge, sample at negative edge"

			config ESP_SDIO_NSEND_NSAMPLE
				bool "Send at negative edge, sample at negative edge"
		endchoice

		config ESP_SDIO_RX_Q_SIZE
			int "SDIO RX queue size"
			default 20
			help
				Very small RX queue will lower ESP <-- SDIO -- Host data rate

		config ESP_SDIO_CHECKSUM
			bool "SDIO checksum ENABLE/DISABLE"
			default n
			help
				ENABLE/DISABLE software SDIO checksum

	endmenu

	menu "SPI Half-duplex Configuration"
		depends on ESP_SPI_HD_HOST_INTERFACE

		choice ESP_SPI_HD_PRIV_MODE
			bool "Slave SPI mode"
			default ESP_SPI_HD_PRIV_MODE_3

			config ESP_SPI_HD_PRIV_MODE_0
				bool "Slave SPI mode 0"

			config ESP_SPI_HD_PRIV_MODE_1
				bool "Slave SPI mode 1"

			config ESP_SPI_HD_PRIV_MODE_2
				bool "Slave SPI mode 2"

			config ESP_SPI_HD_PRIV_MODE_3
				bool "Slave SPI mode 3"
		endchoice

		config ESP_SPI_HD_MODE
			int
			default 0 if ESP_SPI_HD_PRIV_MODE_0
			default 1 if ESP_SPI_HD_PRIV_MODE_1
			default 2 if ESP_SPI_HD_PRIV_MODE_2
			default 3 if ESP_SPI_HD_PRIV_MODE_3
			help
				SPI Mode to use. The same mode must be used on both host and slave.

		choice ESP_SPI_HD_PRIV_INTERFACE_NUM_DATA_LINES
			bool "Num Data Lines to use"
			default ESP_SPI_HD_PRIV_INTERFACE_4_DATA_LINES
			help
				Number of Data Lines to use in the SPI HD interface

			config ESP_SPI_HD_PRIV_INTERFACE_4_DATA_LINES
				bool "4 data lines"

			config ESP_SPI_HD_PRIV_INTERFACE_2_DATA_LINES
				bool "2 data lines"
		endchoice

		config ESP_SPI_HD_INTERFACE_NUM_DATA_LINES
			int
			default 4 if ESP_SPI_HD_PRIV_INTERFACE_4_DATA_LINES
			default 2 if ESP_SPI_HD_PRIV_INTERFACE_2_DATA_LINES

		menu "GPIOs"

			config ESP_SPI_HD_GPIO_CS
				int "Slave GPIO pin for Host CS"
				default 18 if ESP_HOST_DEV_BOARD_P4_FUNC_BOARD
				default 10 if IDF_TARGET_ESP32C5
				default 10
				help
					SPI HD controller Host CS

			config ESP_SPI_HD_GPIO_CLK
				int "Slave GPIO pin for Host CLK"
				default 19 if ESP_HOST_DEV_BOARD_P4_FUNC_BOARD
				default 6 if IDF_TARGET_ESP32C5
				default 12
				help
					SPI HD controller Host CS

			config ESP_SPI_HD_GPIO_D0
				int "Slave GPIO pin for Host D0"
				default 20 if ESP_HOST_DEV_BOARD_P4_FUNC_BOARD
				default 7 if IDF_TARGET_ESP32C5
				default 11
				help
					SPI HD controller Host D0

			config ESP_SPI_HD_GPIO_D1
				int "Slave GPIO pin for Host D1"
				default 21 if ESP_HOST_DEV_BOARD_P4_FUNC_BOARD
				default 2 if IDF_TARGET_ESP32C5
				default 13
				help
					SPI HD controller Host D1

			config ESP_SPI_HD_GPIO_D2
				depends on ESP_SPI_HD_PRIV_INTERFACE_4_DATA_LINES
				int "Slave GPIO pin for Host D2"
				default 22 if ESP_HOST_DEV_BOARD_P4_FUNC_BOARD
				default 5 if IDF_TARGET_ESP32C5
				default 14
				help
					SPI HD controller Host D2

			config ESP_SPI_HD_GPIO_D3
				depends on ESP_SPI_HD_PRIV_INTERFACE_4_DATA_LINES
				int "Slave GPIO pin for Host D3"
				default 23 if ESP_HOST_DEV_BOARD_P4_FUNC_BOARD
				default 4 if IDF_TARGET_ESP32C5
				default 9
				help
					SPI HD controller Host D3

			config ESP_SPI_HD_GPIO_DATA_READY
				int "Slave GPIO pin for Data Ready"
				default 2 if ESP_HOST_DEV_BOARD_P4_FUNC_BOARD
				default 13 if IDF_TARGET_ESP32C5
				default 8
				help
					Slave GPIO pin for indicating host that SPI slave has data to be read by host

			choice ESP_SPI_HD_DATAREADY_GPIO_CONFIG
				bool "DataReady GPIO Config"
				default ESP_DR_ACTIVE_HIGH
				help
					Configure Data Ready to be active high (default) or active low

				config ESP_DR_ACTIVE_HIGH
					bool "Active High"
				config ESP_DR_ACTIVE_LOW
					bool "Active Low"
			endchoice

			config ESP_SPI_HD_GPIO_RESET
				int "Slave GPIO pin to reset itself"
				default -1
				help
					Host uses this pin to reset the slave ESP. To re-use ESP 'RST' or 'EN' GPIO, set value to -1

		endmenu

		config ESP_SPI_HD_Q_SIZE
			int "Queue size"
			default 12 if IDF_TARGET_ESP32C2
			default 10 if IDF_TARGET_ESP32C5
			default 20
			help
				Very small queue will lower SPI HD data rate

		config ESP_SPI_HD_CHECKSUM
			bool "Checksum ENABLE/DISABLE"
			default y
			help
				ENABLE/DISABLE SPI HD software checksum

	endmenu

	menu "UART Configuration"
		depends on ESP_UART_HOST_INTERFACE

		config ESP_UART_PORT
			int "UART Port to Use"
			default 1
			range 0 2 if IDF_TARGET_ESP32
			range 0 1 if IDF_TARGET_ESP32C2 || IDF_TARGET_ESP32C3 || IDF_TARGET_ESP32C5 || IDF_TARGET_ESP32C6
			range 0 2 if IDF_TARGET_ESP32C61
			range 0 1 if IDF_TARGET_ESP32S2
			range 0 2 if IDF_TARGET_ESP32S3
			help
				Select UART Port to Use. Do not select the UART Port used for console output (if enabled)

		config ESP_UART_PIN_TX
			int "TX GPIO number"
			default 13 if IDF_TARGET_ESP32
			default 5 if IDF_TARGET_ESP32C2 || IDF_TARGET_ESP32C3
			default 14 if IDF_TARGET_ESP32C5
			default 21 if IDF_TARGET_ESP32C6
			default 5 if IDF_TARGET_ESP32S2 || IDF_TARGET_ESP32S3
			help
				GPIO used for UART TX

		config ESP_UART_PIN_RX
			int "RX GPIO number"
			default 12 if IDF_TARGET_ESP32
			default 4 if IDF_TARGET_ESP32C2 || IDF_TARGET_ESP32C3
			default 13 if IDF_TARGET_ESP32C5
			default 20 if IDF_TARGET_ESP32C6
			default 4 if IDF_TARGET_ESP32S2 || IDF_TARGET_ESP32S3
			help
				GPIO used for UART RX

		config ESP_UART_BAUDRATE
			int "Baud Rate"
			default 921600
			range 9600 3500000
			help
				Baud Rate to Use. Make sure Hardware supports the rate. Standard rates are 9600, 19200, 38400, 57600, 115200, 460800, 921600

		config ESP_UART_NUM_DATA_BITS
			int "Number of Data Bits"
			default 8
			range 5 8
			help
				Number of Data Bits to use

		choice ESP_UART_PRIV_PARITY
			bool "Parity"

			config ESP_UART_PRIV_PARITY_NONE
				bool "None"

			config ESP_UART_PRIV_PARITY_EVEN
				bool "Even"

			config ESP_UART_PRIV_PARITY_ODD
				bool "Odd"
		endchoice

		config ESP_UART_PARITY
			int
			default 0 if ESP_UART_PRIV_PARITY_NONE
			default 1 if ESP_UART_PRIV_PARITY_EVEN
			default 2 if ESP_UART_PRIV_PARITY_ODD

		choice ESP_UART_PRIV_STOP_BITS
			bool "Number of Stop Bits"

			config ESP_UART_PRIV_STOP_BITS_1
				bool "1"

			config ESP_UART_PRIV_STOP_BITS_1_5
				bool "1.5"

			config ESP_UART_PRIV_STOP_BITS_2
				bool "2"
		endchoice

		config ESP_UART_STOP_BITS
			int
			default 0 if ESP_UART_PRIV_STOP_BITS_1
			default 1 if ESP_UART_PRIV_STOP_BITS_1_5
			default 2 if ESP_UART_PRIV_STOP_BITS_2

		config ESP_UART_GPIO_RESET
			int "Slave GPIO pin to reset itself"
			default -1
			help
				Host uses this pin to reset the slave ESP. To re-use ESP 'RST' or 'EN' GPIO, set value to -1

		config ESP_UART_TX_Q_SIZE
			int "Tx Queue Size"
			default 5
			help
				Very small RX queue will lower ESP -- UART --> Host data rate

		config ESP_UART_RX_Q_SIZE
			int "Rx Queue Size"
			default 5
			help
				Very small RX queue will lower ESP <-- UART -- Host data rate

		config ESP_UART_CHECKSUM
			bool "UART checksum ENABLE/DISABLE"
			default y
			help
				ENABLE/DISABLE software UART checksum
	endmenu

	config ESP_GPIO_SLAVE_RESET
		int
		default ESP_SPI_GPIO_RESET if ESP_SPI_HOST_INTERFACE
		default ESP_SDIO_GPIO_RESET if ESP_SDIO_HOST_INTERFACE
		default ESP_SPI_HD_GPIO_RESET if ESP_SPI_HD_HOST_INTERFACE
		default ESP_UART_GPIO_RESET if ESP_UART_HOST_INTERFACE

	config EXAMPLE_HCI_UART_BAUDRATE
		int "UART Baudrate for HCI: Only applicable for ESP32-C3/ESP32-S3"
		range 115200 921600
		depends on IDF_TARGET_ESP32C3 || IDF_TARGET_ESP32S3
		depends on BT_CTRL_HCI_MODE_UART_H4
		default 921600
		help
			UART Baudrate for HCI over ESP32C3/ESP32S3. Please use standard baudrate.

	config ESP_DEFAULT_TASK_STACK_SIZE
		int "ESP-Hosted task stack size"
		default 4096
		help
			Default task size of ESP-Hosted tasks

	config ESP_DEFAULT_TASK_PRIO
		int "ESP-Hosted task priority"
		default 22
		help
			Default task priority of ESP-Hosted tasks

	config ESP_CACHE_MALLOC
		bool "Enable Mempool"
		default y
		help
			Mempool will help to alloc buffer without going to heap for every memory allocation or free

	config ESP_OTA_WORKAROUND
		bool "OTA workaround - Add sleeps while OTA write"
		default y
		help
			Enable/disable sleeps while OTA operations

	menu "Hosted Debugging"
		config ESP_RAW_THROUGHPUT_TRANSPORT
			bool "RawTP: Transport level throughput debug test"
			default n
				help
					Find max transport performance which helps to assess stability of porting done

		config ESP_RAW_TP_ESP_TO_HOST_PKT_LEN
			depends on ESP_RAW_THROUGHPUT_TRANSPORT
			int "RawTP: ESP to Host packet size"
			range 1 1500
			default 1460

		config ESP_RAW_TP_REPORT_INTERVAL
			depends on ESP_RAW_THROUGHPUT_TRANSPORT
			int "RawTP: periodic duration to report stats accumulated"
			default 10

			config ESP_PKT_STATS
				bool "Transport level packet stats"
				default n
				help
					On comparing with slave packet stats helps to understand any packet loss at hosted
	endmenu
endmenu

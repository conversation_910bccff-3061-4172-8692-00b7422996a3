# Documentation ESP-Hosted-MCU Slave

## Vue d'ensemble

Cette repository contient le firmware **ESP-Hosted-MCU Slave** version 0.0.6, développé par Espressif Systems. Il s'agit d'un adaptateur réseau qui permet à un microcontrôleur hôte de communiquer avec les fonctionnalités WiFi et Bluetooth d'un ESP32 via différentes interfaces de transport.

## Architecture générale

Le projet implémente un **adaptateur réseau esclave** qui expose les capacités WiFi et Bluetooth d'un ESP32 à un microcontrôleur hôte externe. L'ESP32 agit comme un co-processeur réseau contrôlé par l'hôte via des commandes RPC (Remote Procedure Call).

### Composants principaux

1. **Transport Layer** : Gestion des interfaces de communication
2. **Control Layer** : Traitement des commandes RPC
3. **Data Path** : Transmission des données réseau
4. **Protocol Stack** : Support WiFi et Bluetooth
5. **Memory Management** : Gestion optimisée de la mémoire

## Interfaces de transport supportées

Le firmware supporte plusieurs interfaces de transport configurables :

### 1. SDIO (Secure Digital Input/Output)

- **Fichier principal** : `main/sdio_slave_api.c`
- **Configuration** : `CONFIG_ESP_SDIO_HOST_INTERFACE`
- **Chipsets supportés** : ESP32, ESP32-C6
- **Taille buffer max** : 1536 bytes

### 2. SPI (Serial Peripheral Interface)

- **Fichier principal** : `main/spi_slave_api.c`
- **Configuration** : `CONFIG_ESP_SPI_HOST_INTERFACE`
- **Taille buffer max** : 1600 bytes
- **Mode** : Slave SPI

### 3. SPI Half-Duplex

- **Fichier principal** : `main/spi_hd_slave_api.c`
- **Configuration** : `CONFIG_ESP_SPI_HD_HOST_INTERFACE`
- **Taille buffer max** : 1600 bytes

### 4. UART (Universal Asynchronous Receiver-Transmitter)

- **Fichier principal** : `main/uart_slave_api.c`
- **Configuration** : `CONFIG_ESP_UART_HOST_INTERFACE`
- **Taille buffer max** : 1600 bytes
- **Support flow control** : Oui

## Structure des fichiers principaux

### Fichiers de configuration

- **`CMakeLists.txt`** : Configuration du build système
- **`sdkconfig.defaults`** : Configuration par défaut du SDK
- **`partitions.*.csv`** : Tables de partitions pour différents chipsets

### Fichiers source principaux

#### `main/app_main.c` (944 lignes)

- Point d'entrée principal de l'application
- Initialisation des composants système
- Gestion des tâches FreeRTOS
- Configuration des interfaces de transport

#### `main/slave_control.c` (2675 lignes)

- Implémentation du contrôleur RPC
- Gestion des commandes WiFi et Bluetooth
- Traitement des événements système
- Interface avec les APIs ESP-IDF

#### `main/interface.h`

- Définitions des interfaces de transport
- Structures de données communes
- Énumérations des types d'interfaces

### Gestion mémoire

- **`main/mempool.c`** : Pool de mémoire optimisé
- **`main/mempool_ll.c`** : Implémentation low-level du pool
- Support pour mémoire pré-allouée et allocation dynamique

### Support Bluetooth

- **`main/slave_bt.c`** : Interface Bluetooth HCI
- Support BLE et BR/EDR
- Intégration avec le stack Bluetooth ESP-IDF

### Statistiques et debugging

- **`main/stats.c`** : Collecte de statistiques de performance
- Support pour FreeRTOS runtime stats
- Monitoring des buffers et queues

## Protocole de communication

### Protocol Buffers (Protobuf)

Le projet utilise **Protocol Buffers** pour la sérialisation des messages RPC :

- **Fichier proto** : `main/common/proto/esp_hosted_rpc.proto`
- **Fichiers générés** : `esp_hosted_rpc.pb-c.h/c`
- **Bibliothèque** : protobuf-c (submodule)

### Types de messages RPC

1. **Requests** : Commandes envoyées par l'hôte
2. **Responses** : Réponses de l'ESP32
3. **Events** : Notifications asynchrones

### Exemples de commandes supportées

- `Req_GetMACAddress` : Obtenir l'adresse MAC
- `Req_SetWifiMode` : Configurer le mode WiFi
- `Req_WifiConnect` : Se connecter à un AP
- `Req_OTA_Begin/Write/End` : Mise à jour OTA

## Configuration du projet

### Chipsets supportés

- ESP32
- ESP32-C2
- ESP32-C3
- ESP32-C5
- ESP32-C6
- ESP32-S3

### Configuration par défaut

```
CONFIG_BT_ENABLED=y
CONFIG_BT_CONTROLLER_ONLY=y
CONFIG_ESPTOOLPY_FLASHSIZE_4MB=y
CONFIG_PARTITION_TABLE_TWO_OTA=y
CONFIG_FREERTOS_HZ=1000
```

## Capacités du système

Le firmware expose les capacités suivantes à l'hôte :

- **WiFi SDIO/SPI Support** : Communication WiFi via SDIO ou SPI
- **Bluetooth Support** : HCI over UART/SDIO/SPI
- **BLE Only/BR-EDR Only** : Support sélectif Bluetooth
- **Checksum Enabled** : Vérification d'intégrité des données

## Flux de données

1. **Initialisation** : Configuration des interfaces et du stack réseau
2. **Handshake** : Échange des capacités avec l'hôte
3. **Command Processing** : Traitement des commandes RPC
4. **Data Transfer** : Transmission bidirectionnelle des données
5. **Event Notification** : Envoi d'événements asynchrones

## Build et déploiement

Le projet utilise le système de build ESP-IDF avec CMake :

```bash
idf.py build
idf.py flash
```

### Composants ESP-IDF requis

- `esp_timer`
- `esptool_py`
- `bootloader`
- `nvs_flash`
- `esp_rom`
- `esp_wifi`
- `protocomm`
- `driver`

## Debugging et monitoring

- **Logs ESP** : Système de logging intégré
- **Statistics** : Collecte de métriques de performance
- **Memory dumps** : Monitoring de l'utilisation mémoire
- **Task monitoring** : Surveillance des tâches FreeRTOS

## Détails techniques avancés

### Gestion des queues et priorités

Le système utilise plusieurs queues FreeRTOS pour gérer les différents types de trafic :

- **Queue de transmission** : Gestion des paquets sortants
- **Queue de réception** : Traitement des paquets entrants
- **Queue Bluetooth** : Trafic HCI spécialisé
- **Queue de contrôle** : Messages RPC prioritaires

### Memory Pool Management

Le système implémente un gestionnaire de pool mémoire optimisé :

```c
struct hosted_mempool {
    struct os_mempool *pool;
    uint8_t *heap;
    size_t block_size;
    size_t num_blocks;
};
```

**Avantages** :

- Allocation/libération rapide
- Fragmentation réduite
- Support mémoire pré-allouée
- Alignement mémoire optimisé

### Interface Transport Layer

Chaque interface de transport implémente la structure `if_ops_t` :

```c
typedef struct {
    interface_handle_t* (*init)(void);
    int32_t (*write)(interface_handle_t*, interface_buffer_handle_t*);
    int (*read)(interface_handle_t*, interface_buffer_handle_t*);
    esp_err_t (*reset)(interface_handle_t*);
    void (*deinit)(interface_handle_t*);
} if_ops_t;
```

### Buffer Management

Structure unifiée pour tous les types de buffers :

```c
typedef struct {
    union {
        sdio_slave_buf_handle_t sdio_buf_handle;
        spi_slave_hd_data_t* spi_hd_trans_handle;
        wlan_buf_handle_t wlan_buf_handle;
        void* priv_buffer_handle;
    };
    uint8_t if_type;
    uint8_t if_num;
    uint8_t* payload;
    uint16_t payload_len;
    uint16_t seq_num;
    void (*free_buf_handle)(void* buf_handle);
} interface_buffer_handle_t;
```

### États des interfaces

```c
typedef enum {
    DEINIT,   // Interface non initialisée
    INIT,     // Interface initialisée mais inactive
    ACTIVE,   // Interface active et opérationnelle
    DEACTIVE  // Interface désactivée temporairement
} INTERFACE_STATE;
```

### Types d'interfaces supportées

```c
typedef enum {
    ESP_INVALID_IF,  // Interface invalide
    ESP_STA_IF,      // Interface Station WiFi
    ESP_AP_IF,       // Interface Access Point WiFi
    ESP_SERIAL_IF,   // Interface série (contrôle)
    ESP_HCI_IF,      // Interface Bluetooth HCI
    ESP_PRIV_IF,     // Interface privée
    ESP_TEST_IF,     // Interface de test
    ESP_ETH_IF,      // Interface Ethernet
    ESP_MAX_IF       // Limite maximale
} esp_hosted_if_type_t;
```

## Configuration des GPIO

### SDIO Interface

- **CLK** : GPIO configuré selon le chipset
- **CMD** : GPIO configuré selon le chipset
- **D0-D3** : Lignes de données SDIO

### SPI Interface

- **MOSI** : Master Out Slave In
- **MISO** : Master In Slave Out
- **SCLK** : Serial Clock
- **CS** : Chip Select
- **Handshake** : GPIO pour signalisation
- **Data Ready** : GPIO pour notification

### UART Interface

- **TX** : Transmission
- **RX** : Réception
- **RTS/CTS** : Flow control (optionnel)

## Protocole de handshake

1. **Reset** : L'hôte déclenche un reset via GPIO
2. **Initialization** : L'ESP32 initialise ses composants
3. **Capability Exchange** : Échange des capacités supportées
4. **Interface Setup** : Configuration de l'interface de transport
5. **Ready State** : Système prêt pour les commandes

## Gestion des erreurs

Le système implémente plusieurs niveaux de gestion d'erreurs :

- **Transport Level** : Retry automatique, timeout
- **Protocol Level** : Checksum, numéros de séquence
- **Application Level** : Codes d'erreur RPC
- **System Level** : Watchdog, recovery automatique

## Performance et optimisations

### Optimisations mémoire

- Pool de buffers pré-alloués
- Zero-copy quand possible
- Alignement mémoire optimisé
- Garbage collection minimisé

### Optimisations réseau

- Queues prioritaires
- Batching des paquets
- Flow control adaptatif
- Compression optionnelle

### Optimisations CPU

- Interruptions optimisées
- DMA pour les transferts
- Cache management
- Task scheduling optimisé

## Sécurité

- **Authentification** : Support WPA/WPA2/WPA3
- **Chiffrement** : AES, TKIP
- **Certificats** : Support Enterprise
- **OTA sécurisé** : Signature et vérification

## Cas d'usage typiques

1. **IoT Gateway** : ESP32 comme passerelle WiFi/BT
2. **Sensor Hub** : Collecte de données avec connectivité
3. **Industrial Controller** : Contrôle industriel avec réseau
4. **Smart Home** : Hub domotique multi-protocole
5. **Wearable Device** : Dispositif portable connecté

## Limitations et considérations

- **Mémoire** : Limitée par la RAM disponible
- **Throughput** : Dépendant de l'interface de transport
- **Latence** : Impact du protocole RPC
- **Power** : Consommation selon les modes actifs
- **Compatibilité** : Dépendante de la version ESP-IDF

## Support et maintenance

- **Version actuelle** : 0.0.6
- **License** : Apache 2.0
- **Support** : Espressif Systems
- **Documentation** : ESP-IDF Programming Guide
- **Community** : Forums Espressif
